#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة ويب لعميل API إيرثلنك
============================
"""

from flask import Flask, render_template, request, jsonify, session, redirect, url_for
import json
import os
from earthlink_api_client import EarthlinkAPIClient
import logging

app = Flask(__name__)
app.secret_key = 'earthlink_secret_key_2025'

# إعداد نظام السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# متغير عام لعميل API
api_client = None

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return render_template('index.html')

@app.route('/login', methods=['POST'])
def login():
    """تسجيل الدخول"""
    global api_client
    
    data = request.get_json()
    username = data.get('username', '')
    password = data.get('password', '')
    base_url = data.get('base_url', 'http://rapi.earthlink.iq')
    
    try:
        api_client = EarthlinkAPIClient(base_url)
        
        if api_client.login(username, password):
            session['logged_in'] = True
            session['username'] = username
            return jsonify({
                'success': True,
                'message': 'تم تسجيل الدخول بنجاح'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'فشل في تسجيل الدخول'
            })
            
    except Exception as e:
        logger.error(f"خطأ في تسجيل الدخول: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ: {str(e)}'
        })

@app.route('/explore')
def explore():
    """استكشاف API"""
    global api_client
    
    if not api_client or not api_client.is_authenticated:
        return jsonify({
            'success': False,
            'message': 'يجب تسجيل الدخول أولاً'
        })
    
    try:
        api_info = api_client.explore_api()
        return jsonify({
            'success': True,
            'data': api_info
        })
    except Exception as e:
        logger.error(f"خطأ في استكشاف API: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ: {str(e)}'
        })

@app.route('/subscribers')
def get_subscribers():
    """جلب المشتركين"""
    global api_client
    
    if not api_client or not api_client.is_authenticated:
        return jsonify({
            'success': False,
            'message': 'يجب تسجيل الدخول أولاً'
        })
    
    try:
        subscribers = api_client.get_subscribers()
        return jsonify({
            'success': True,
            'data': subscribers,
            'count': len(subscribers)
        })
    except Exception as e:
        logger.error(f"خطأ في جلب المشتركين: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ: {str(e)}'
        })

@app.route('/subscriptions')
def get_subscriptions():
    """جلب الاشتراكات"""
    global api_client
    
    if not api_client or not api_client.is_authenticated:
        return jsonify({
            'success': False,
            'message': 'يجب تسجيل الدخول أولاً'
        })
    
    try:
        subscriptions = api_client.get_subscriptions()
        return jsonify({
            'success': True,
            'data': subscriptions,
            'count': len(subscriptions)
        })
    except Exception as e:
        logger.error(f"خطأ في جلب الاشتراكات: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ: {str(e)}'
        })

@app.route('/logout')
def logout():
    """تسجيل الخروج"""
    global api_client
    
    session.clear()
    api_client = None
    
    return jsonify({
        'success': True,
        'message': 'تم تسجيل الخروج'
    })

@app.route('/status')
def status():
    """حالة الاتصال"""
    global api_client
    
    return jsonify({
        'logged_in': api_client is not None and api_client.is_authenticated,
        'username': session.get('username', ''),
        'session_active': 'logged_in' in session
    })

if __name__ == '__main__':
    # إنشاء مجلد templates إذا لم يكن موجوداً
    if not os.path.exists('templates'):
        os.makedirs('templates')
    
    print("🌐 تشغيل واجهة ويب إيرثلنك API")
    print("الرابط: http://localhost:5000")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
