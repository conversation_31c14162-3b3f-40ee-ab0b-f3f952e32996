#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مستخرج البيانات من API إيرثلنك
===============================
أداة متخصصة لاستخراج البيانات الفعلية من API
"""

import requests
import json
import time
import logging
from typing import Dict, List, Optional, Any
import urllib3
from urllib.parse import urljoin, urlencode
import base64
from requests.auth import HTTPBasicAuth
import itertools
import xml.etree.ElementTree as ET
from bs4 import BeautifulSoup
import re

# تعطيل تحذيرات SSL
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data_extractor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class EarthlinkDataExtractor:
    """مستخرج البيانات من API إيرثلنك"""
    
    def __init__(self):
        """تهيئة المستخرج"""
        self.logger = logging.getLogger(__name__)
        self.base_url = "http://rapi.earthlink.iq"
        self.api_endpoint = f"{self.base_url}/api"
        self.session = requests.Session()
        self.extracted_data = {
            "subscribers": [],
            "subscriptions": [],
            "users": [],
            "billing": [],
            "reports": [],
            "raw_responses": [],
            "successful_endpoints": []
        }
        
        self._setup_session()
    
    def _setup_session(self):
        """إعداد session متقدم"""
        self.session.headers.update({
            'User-Agent': 'EarthlinkDataExtractor/1.0',
            'Accept': 'application/json, application/xml, text/html, text/plain, */*',
            'Accept-Language': 'ar,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive'
        })
        
        self.session.verify = False
        self.session.timeout = 20
    
    def extract_all_data(self, username: str, password: str) -> Dict:
        """استخراج جميع البيانات المتاحة"""
        self.logger.info("بدء استخراج البيانات")
        
        print("📊 مستخرج البيانات من API إيرثلنك")
        print("=" * 50)
        
        # تجربة طرق مصادقة مختلفة
        print("🔐 اختبار طرق المصادقة...")
        auth_methods = self._get_working_auth_methods(username, password)
        
        if not auth_methods:
            print("❌ لم تنجح أي طريقة مصادقة")
            return self.extracted_data
        
        print(f"✅ وجدت {len(auth_methods)} طريقة مصادقة ناجحة")
        
        # استخدام أفضل طريقة مصادقة
        best_auth = auth_methods[0]
        print(f"🎯 استخدام طريقة: {best_auth['method']}")
        
        # تطبيق المصادقة
        self._apply_authentication(best_auth, username, password)
        
        # استكشاف نقاط البيانات
        print("🔍 استكشاف نقاط البيانات...")
        self._explore_data_endpoints()
        
        # استخراج بيانات المشتركين
        print("👥 استخراج بيانات المشتركين...")
        self._extract_subscribers()
        
        # استخراج بيانات الاشتراكات
        print("📋 استخراج بيانات الاشتراكات...")
        self._extract_subscriptions()
        
        # استخراج بيانات المستخدمين
        print("👤 استخراج بيانات المستخدمين...")
        self._extract_users()
        
        # استخراج بيانات الفواتير
        print("💰 استخراج بيانات الفواتير...")
        self._extract_billing()
        
        # استخراج التقارير
        print("📈 استخراج التقارير...")
        self._extract_reports()
        
        # محاولة استخراج بيانات إضافية
        print("🔎 البحث عن بيانات إضافية...")
        self._extract_additional_data()
        
        return self.extracted_data
    
    def _get_working_auth_methods(self, username: str, password: str) -> List[Dict]:
        """الحصول على طرق المصادقة الناجحة"""
        working_methods = []
        
        # Basic Authentication
        try:
            response = self.session.get(self.api_endpoint, auth=HTTPBasicAuth(username, password))
            if response.status_code == 200:
                working_methods.append({
                    "method": "Basic Auth",
                    "type": "auth",
                    "response": response
                })
        except:
            pass
        
        # API Key في Headers
        api_headers = [
            {'Authorization': f'Bearer {password}'},
            {'X-API-Key': password},
            {'Authorization': f'Token {password}'}
        ]
        
        for headers in api_headers:
            try:
                response = self.session.get(self.api_endpoint, headers=headers)
                if response.status_code == 200:
                    working_methods.append({
                        "method": f"API Key - {list(headers.keys())[0]}",
                        "type": "headers",
                        "headers": headers,
                        "response": response
                    })
            except:
                pass
        
        # POST Authentication
        login_data = {"username": username, "password": password}
        try:
            response = self.session.post(self.api_endpoint, json=login_data)
            if response.status_code == 200:
                working_methods.append({
                    "method": "POST JSON",
                    "type": "post",
                    "data": login_data,
                    "response": response
                })
        except:
            pass
        
        return working_methods
    
    def _apply_authentication(self, auth_method: Dict, username: str, password: str):
        """تطبيق طريقة المصادقة"""
        if auth_method["type"] == "auth":
            self.session.auth = HTTPBasicAuth(username, password)
        elif auth_method["type"] == "headers":
            self.session.headers.update(auth_method["headers"])
        elif auth_method["type"] == "post":
            # إرسال طلب تسجيل دخول أولاً
            self.session.post(self.api_endpoint, json=auth_method["data"])
    
    def _explore_data_endpoints(self):
        """استكشاف نقاط البيانات"""
        data_endpoints = [
            # نقاط المشتركين
            '/subscribers', '/users', '/customers', '/clients',
            '/subscriber', '/user', '/customer', '/client',
            '/getsubscribers', '/getusers', '/getcustomers',
            '/listsubscribers', '/listusers', '/listcustomers',
            
            # نقاط الاشتراكات
            '/subscriptions', '/plans', '/packages', '/services',
            '/subscription', '/plan', '/package', '/service',
            '/getsubscriptions', '/getplans', '/getpackages',
            '/listsubscriptions', '/listplans', '/listpackages',
            
            # نقاط الفواتير
            '/billing', '/invoices', '/payments', '/transactions',
            '/bills', '/invoice', '/payment', '/transaction',
            '/getbilling', '/getinvoices', '/getpayments',
            '/listbilling', '/listinvoices', '/listpayments',
            
            # نقاط التقارير
            '/reports', '/stats', '/analytics', '/dashboard',
            '/report', '/stat', '/analytic',
            '/getreports', '/getstats', '/getanalytics',
            '/listreports', '/liststats', '/listanalytics',
            
            # نقاط إدارية
            '/admin', '/management', '/control',
            '/getdata', '/data', '/info', '/information',
            '/export', '/download', '/backup',
            
            # نقاط API شائعة
            '/list', '/get', '/fetch', '/retrieve',
            '/all', '/everything', '/complete'
        ]
        
        successful_endpoints = []
        
        for endpoint in data_endpoints:
            url = f"{self.api_endpoint}{endpoint}"
            
            try:
                # اختبار GET
                response = self.session.get(url)
                if response.status_code == 200 and len(response.content) > 0:
                    successful_endpoints.append({
                        "endpoint": endpoint,
                        "url": url,
                        "method": "GET",
                        "status_code": response.status_code,
                        "content_length": len(response.content),
                        "content_type": response.headers.get('content-type', ''),
                        "response": response
                    })
                
                # اختبار POST
                response = self.session.post(url)
                if response.status_code == 200 and len(response.content) > 0:
                    successful_endpoints.append({
                        "endpoint": endpoint,
                        "url": url,
                        "method": "POST",
                        "status_code": response.status_code,
                        "content_length": len(response.content),
                        "content_type": response.headers.get('content-type', ''),
                        "response": response
                    })
                    
            except Exception as e:
                self.logger.warning(f"خطأ في اختبار {endpoint}: {str(e)}")
        
        self.extracted_data["successful_endpoints"] = successful_endpoints
        print(f"  📊 وجدت {len(successful_endpoints)} نقطة بيانات متاحة")
    
    def _extract_subscribers(self):
        """استخراج بيانات المشتركين"""
        subscriber_keywords = ['subscriber', 'user', 'customer', 'client']
        
        for endpoint_info in self.extracted_data["successful_endpoints"]:
            endpoint = endpoint_info["endpoint"].lower()
            
            if any(keyword in endpoint for keyword in subscriber_keywords):
                try:
                    response = endpoint_info["response"]
                    data = self._parse_response(response)
                    
                    if data:
                        self.extracted_data["subscribers"].append({
                            "source": endpoint_info["endpoint"],
                            "data": data,
                            "raw_content": response.text[:1000]
                        })
                        
                except Exception as e:
                    self.logger.warning(f"خطأ في استخراج المشتركين من {endpoint}: {str(e)}")
        
        # محاولة طلبات مخصصة للمشتركين
        subscriber_requests = [
            {'action': 'getsubscribers'},
            {'cmd': 'listusers'},
            {'method': 'subscribers'},
            {'function': 'getusers'},
            {'type': 'customers'},
            {'format': 'json', 'data': 'subscribers'}
        ]
        
        for params in subscriber_requests:
            try:
                response = self.session.get(self.api_endpoint, params=params)
                if response.status_code == 200 and len(response.content) > 0:
                    data = self._parse_response(response)
                    if data:
                        self.extracted_data["subscribers"].append({
                            "source": f"params_{list(params.keys())[0]}",
                            "data": data,
                            "raw_content": response.text[:1000]
                        })
            except:
                pass
        
        print(f"  👥 استخرجت {len(self.extracted_data['subscribers'])} مجموعة بيانات مشتركين")
    
    def _extract_subscriptions(self):
        """استخراج بيانات الاشتراكات"""
        subscription_keywords = ['subscription', 'plan', 'package', 'service']
        
        for endpoint_info in self.extracted_data["successful_endpoints"]:
            endpoint = endpoint_info["endpoint"].lower()
            
            if any(keyword in endpoint for keyword in subscription_keywords):
                try:
                    response = endpoint_info["response"]
                    data = self._parse_response(response)
                    
                    if data:
                        self.extracted_data["subscriptions"].append({
                            "source": endpoint_info["endpoint"],
                            "data": data,
                            "raw_content": response.text[:1000]
                        })
                        
                except Exception as e:
                    self.logger.warning(f"خطأ في استخراج الاشتراكات من {endpoint}: {str(e)}")
        
        print(f"  📋 استخرجت {len(self.extracted_data['subscriptions'])} مجموعة بيانات اشتراكات")
    
    def _extract_users(self):
        """استخراج بيانات المستخدمين"""
        # محاولة طلبات مخصصة للمستخدمين
        user_requests = [
            {'action': 'getusers'},
            {'cmd': 'userlist'},
            {'method': 'users'},
            {'function': 'getuserdata'},
            {'type': 'userinfo'}
        ]
        
        for params in user_requests:
            try:
                response = self.session.get(self.api_endpoint, params=params)
                if response.status_code == 200 and len(response.content) > 0:
                    data = self._parse_response(response)
                    if data:
                        self.extracted_data["users"].append({
                            "source": f"params_{list(params.keys())[0]}",
                            "data": data,
                            "raw_content": response.text[:1000]
                        })
            except:
                pass
        
        print(f"  👤 استخرجت {len(self.extracted_data['users'])} مجموعة بيانات مستخدمين")
    
    def _extract_billing(self):
        """استخراج بيانات الفواتير"""
        billing_keywords = ['billing', 'invoice', 'payment', 'transaction']
        
        for endpoint_info in self.extracted_data["successful_endpoints"]:
            endpoint = endpoint_info["endpoint"].lower()
            
            if any(keyword in endpoint for keyword in billing_keywords):
                try:
                    response = endpoint_info["response"]
                    data = self._parse_response(response)
                    
                    if data:
                        self.extracted_data["billing"].append({
                            "source": endpoint_info["endpoint"],
                            "data": data,
                            "raw_content": response.text[:1000]
                        })
                        
                except Exception as e:
                    self.logger.warning(f"خطأ في استخراج الفواتير من {endpoint}: {str(e)}")
        
        print(f"  💰 استخرجت {len(self.extracted_data['billing'])} مجموعة بيانات فواتير")
    
    def _extract_reports(self):
        """استخراج التقارير"""
        report_keywords = ['report', 'stat', 'analytic', 'dashboard']
        
        for endpoint_info in self.extracted_data["successful_endpoints"]:
            endpoint = endpoint_info["endpoint"].lower()
            
            if any(keyword in endpoint for keyword in report_keywords):
                try:
                    response = endpoint_info["response"]
                    data = self._parse_response(response)
                    
                    if data:
                        self.extracted_data["reports"].append({
                            "source": endpoint_info["endpoint"],
                            "data": data,
                            "raw_content": response.text[:1000]
                        })
                        
                except Exception as e:
                    self.logger.warning(f"خطأ في استخراج التقارير من {endpoint}: {str(e)}")
        
        print(f"  📈 استخرجت {len(self.extracted_data['reports'])} مجموعة تقارير")
    
    def _extract_additional_data(self):
        """استخراج بيانات إضافية"""
        # حفظ جميع الاستجابات الخام
        for endpoint_info in self.extracted_data["successful_endpoints"]:
            try:
                response = endpoint_info["response"]
                self.extracted_data["raw_responses"].append({
                    "endpoint": endpoint_info["endpoint"],
                    "url": endpoint_info["url"],
                    "method": endpoint_info["method"],
                    "content_type": endpoint_info["content_type"],
                    "content": response.text,
                    "headers": dict(response.headers)
                })
            except:
                pass
        
        print(f"  🔎 حفظت {len(self.extracted_data['raw_responses'])} استجابة خام")
    
    def _parse_response(self, response) -> Optional[Any]:
        """تحليل الاستجابة"""
        try:
            content_type = response.headers.get('content-type', '').lower()
            
            # JSON
            if 'json' in content_type or response.text.strip().startswith('{'):
                return response.json()
            
            # XML
            elif 'xml' in content_type or response.text.strip().startswith('<'):
                root = ET.fromstring(response.text)
                return self._xml_to_dict(root)
            
            # HTML
            elif 'html' in content_type:
                soup = BeautifulSoup(response.text, 'html.parser')
                return self._extract_from_html(soup)
            
            # Text
            else:
                return {"text_content": response.text}
                
        except Exception as e:
            self.logger.warning(f"خطأ في تحليل الاستجابة: {str(e)}")
            return None
    
    def _xml_to_dict(self, element) -> Dict:
        """تحويل XML إلى قاموس"""
        result = {}
        
        if element.text and element.text.strip():
            result['text'] = element.text.strip()
        
        for child in element:
            child_data = self._xml_to_dict(child)
            if child.tag in result:
                if not isinstance(result[child.tag], list):
                    result[child.tag] = [result[child.tag]]
                result[child.tag].append(child_data)
            else:
                result[child.tag] = child_data
        
        return result
    
    def _extract_from_html(self, soup) -> Dict:
        """استخراج البيانات من HTML"""
        data = {}
        
        # استخراج الجداول
        tables = soup.find_all('table')
        if tables:
            data['tables'] = []
            for i, table in enumerate(tables):
                table_data = []
                rows = table.find_all('tr')
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    row_data = [cell.get_text(strip=True) for cell in cells]
                    if row_data:
                        table_data.append(row_data)
                if table_data:
                    data['tables'].append(table_data)
        
        # استخراج القوائم
        lists = soup.find_all(['ul', 'ol'])
        if lists:
            data['lists'] = []
            for lst in lists:
                items = lst.find_all('li')
                list_data = [item.get_text(strip=True) for item in items]
                if list_data:
                    data['lists'].append(list_data)
        
        # استخراج النصوص
        text_content = soup.get_text(strip=True)
        if text_content:
            data['text'] = text_content[:500]  # أول 500 حرف
        
        return data
    
    def generate_summary_report(self) -> str:
        """إنشاء تقرير ملخص"""
        report = []
        report.append("📊 تقرير استخراج البيانات من API إيرثلنك")
        report.append("=" * 60)
        
        # إحصائيات عامة
        total_endpoints = len(self.extracted_data["successful_endpoints"])
        total_subscribers = len(self.extracted_data["subscribers"])
        total_subscriptions = len(self.extracted_data["subscriptions"])
        total_users = len(self.extracted_data["users"])
        total_billing = len(self.extracted_data["billing"])
        total_reports = len(self.extracted_data["reports"])
        total_raw = len(self.extracted_data["raw_responses"])
        
        report.append(f"\n📈 إحصائيات الاستخراج:")
        report.append(f"  - نقاط البيانات المكتشفة: {total_endpoints}")
        report.append(f"  - مجموعات بيانات المشتركين: {total_subscribers}")
        report.append(f"  - مجموعات بيانات الاشتراكات: {total_subscriptions}")
        report.append(f"  - مجموعات بيانات المستخدمين: {total_users}")
        report.append(f"  - مجموعات بيانات الفواتير: {total_billing}")
        report.append(f"  - مجموعات التقارير: {total_reports}")
        report.append(f"  - الاستجابات الخام: {total_raw}")
        
        # تفاصيل النقاط الناجحة
        if self.extracted_data["successful_endpoints"]:
            report.append(f"\n✅ نقاط البيانات الناجحة:")
            for endpoint in self.extracted_data["successful_endpoints"]:
                report.append(f"  - {endpoint['endpoint']} ({endpoint['method']}) - {endpoint['content_length']} بايت")
        
        # عينات من البيانات
        if self.extracted_data["subscribers"]:
            report.append(f"\n👥 عينة من بيانات المشتركين:")
            for i, sub in enumerate(self.extracted_data["subscribers"][:3]):
                report.append(f"  {i+1}. المصدر: {sub['source']}")
        
        if self.extracted_data["subscriptions"]:
            report.append(f"\n📋 عينة من بيانات الاشتراكات:")
            for i, sub in enumerate(self.extracted_data["subscriptions"][:3]):
                report.append(f"  {i+1}. المصدر: {sub['source']}")
        
        return "\n".join(report)


def main():
    """الدالة الرئيسية"""
    print("📊 مستخرج البيانات من API إيرثلنك")
    print("=" * 50)
    
    # بيانات تسجيل الدخول
    username = "admin@adhamm1"
    password = "adham12398071@@11"
    
    # إنشاء المستخرج
    extractor = EarthlinkDataExtractor()
    
    # تشغيل الاستخراج
    extracted_data = extractor.extract_all_data(username, password)
    
    # حفظ البيانات المستخرجة
    with open('extracted_earthlink_data.json', 'w', encoding='utf-8') as f:
        # تحويل response objects إلى نص قابل للتسلسل
        serializable_data = {}
        for key, value in extracted_data.items():
            if key == "successful_endpoints":
                serializable_data[key] = []
                for endpoint in value:
                    endpoint_copy = endpoint.copy()
                    if 'response' in endpoint_copy:
                        del endpoint_copy['response']  # إزالة response object
                    serializable_data[key].append(endpoint_copy)
            else:
                serializable_data[key] = value
        
        json.dump(serializable_data, f, ensure_ascii=False, indent=2)
    
    print("\n💾 تم حفظ البيانات المستخرجة في extracted_earthlink_data.json")
    
    # إنشاء التقرير
    report = extractor.generate_summary_report()
    print("\n" + report)
    
    # حفظ التقرير
    with open('extraction_summary_report.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print("\n📄 تم حفظ التقرير في extraction_summary_report.txt")


if __name__ == "__main__":
    main()
