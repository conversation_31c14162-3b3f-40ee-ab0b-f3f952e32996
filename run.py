#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل سريع لأدوات إيرثلنك API
==============================
"""

import sys
import os
import subprocess
import json
from earthlink_api_client import EarthlinkAPIClient

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 تثبيت المتطلبات...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ تم تثبيت المتطلبات بنجاح")
        return True
    except Exception as e:
        print(f"❌ فشل في تثبيت المتطلبات: {str(e)}")
        return False

def test_api_client():
    """اختبار عميل API"""
    print("\n🔧 اختبار عميل API...")
    
    # بيانات تسجيل الدخول
    username = "admin@adhamm1"
    password = "adham12398071@@11"
    
    try:
        # إنشاء عميل API
        client = EarthlinkAPIClient()
        
        # محاولة تسجيل الدخول
        print(f"🔐 محاولة تسجيل الدخول للمستخدم: {username}")
        
        if client.login(username, password):
            print("✅ تم تسجيل الدخول بنجاح!")
            
            # استكشاف API
            print("🔍 استكشاف API...")
            api_info = client.explore_api()
            
            print(f"📊 تم اكتشاف {len(api_info.get('endpoints', []))} نقطة نهاية")
            
            # جلب المشتركين
            print("👥 جلب قائمة المشتركين...")
            subscribers = client.get_subscribers()
            print(f"تم العثور على {len(subscribers)} مشترك")
            
            # جلب الاشتراكات
            print("📋 جلب قائمة الاشتراكات...")
            subscriptions = client.get_subscriptions()
            print(f"تم العثور على {len(subscriptions)} اشتراك")
            
            # حفظ النتائج
            results = {
                'api_info': api_info,
                'subscribers': subscribers,
                'subscriptions': subscriptions,
                'login_successful': True
            }
            
            with open('api_test_results.json', 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            print("💾 تم حفظ نتائج الاختبار في api_test_results.json")
            return True
            
        else:
            print("❌ فشل في تسجيل الدخول")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار API: {str(e)}")
        return False

def run_selenium_explorer():
    """تشغيل مستكشف Selenium"""
    print("\n🔍 تشغيل مستكشف Selenium...")
    
    try:
        from selenium_explorer import EarthlinkSeleniumExplorer
        
        # بيانات تسجيل الدخول
        username = "admin@adhamm1"
        password = "adham12398071@@11"
        
        # إنشاء المستكشف
        explorer = EarthlinkSeleniumExplorer(headless=True)  # تشغيل في الخلفية
        
        # بدء الاستكشاف
        print("🚀 بدء استكشاف الموقع...")
        results = explorer.explore_site(username, password)
        
        # حفظ النتائج
        with open('selenium_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print("💾 تم حفظ نتائج Selenium في selenium_results.json")
        
        # طباعة ملخص
        print("\n📊 ملخص نتائج Selenium:")
        print(f"- عنوان الموقع: {results.get('site_info', {}).get('title', 'غير متاح')}")
        print(f"- عدد النماذج: {len(results.get('forms_found', []))}")
        print(f"- عدد محاولات تسجيل الدخول: {len(results.get('login_attempts', []))}")
        
        login_success = any(attempt.get('success', False) for attempt in results.get('login_attempts', []))
        print(f"- حالة تسجيل الدخول: {'✅ نجح' if login_success else '❌ فشل'}")
        
        return True
        
    except ImportError:
        print("⚠️ Selenium غير متاح، تخطي هذا الاختبار")
        return False
    except Exception as e:
        print(f"❌ خطأ في Selenium: {str(e)}")
        return False

def start_web_interface():
    """تشغيل واجهة الويب"""
    print("\n🌐 تشغيل واجهة الويب...")
    print("الرابط: http://localhost:5000")
    print("اضغط Ctrl+C للإيقاف")
    
    try:
        from web_interface import app
        app.run(debug=False, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف الخادم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل واجهة الويب: {str(e)}")

def main():
    """الدالة الرئيسية"""
    print("🌐 أدوات إيرثلنك API")
    print("=" * 50)
    
    while True:
        print("\nاختر العملية:")
        print("1. تثبيت المتطلبات")
        print("2. اختبار عميل API")
        print("3. تشغيل مستكشف Selenium")
        print("4. تشغيل واجهة الويب")
        print("5. تشغيل جميع الاختبارات")
        print("0. خروج")
        
        choice = input("\nأدخل اختيارك (0-5): ").strip()
        
        if choice == "1":
            install_requirements()
        
        elif choice == "2":
            test_api_client()
        
        elif choice == "3":
            run_selenium_explorer()
        
        elif choice == "4":
            start_web_interface()
        
        elif choice == "5":
            print("🚀 تشغيل جميع الاختبارات...")
            install_requirements()
            test_api_client()
            run_selenium_explorer()
            print("\n✅ تم الانتهاء من جميع الاختبارات")
            print("📁 تحقق من الملفات التالية للنتائج:")
            print("  - api_test_results.json")
            print("  - selenium_results.json")
            print("  - earthlink_api.log")
            print("  - selenium_explorer.log")
        
        elif choice == "0":
            print("👋 وداعاً!")
            break
        
        else:
            print("❌ اختيار غير صحيح")

if __name__ == "__main__":
    main()
