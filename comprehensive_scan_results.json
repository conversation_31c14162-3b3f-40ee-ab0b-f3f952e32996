{"network_analysis": {"dns_resolution": {"hostname": "rapi.earthlink.iq", "ip_addresses": ["*************"], "aliases": []}, "port_scan": {"open_ports": [443, 80], "closed_ports": [5000, 8443, 8080, 9000, 8000, 3000]}, "ssl_analysis": {}, "response_analysis": {"status_code": 403, "headers": {"content-type": "text/html", "server": "Microsoft-IIS/10.0", "x-powered-by": "ASP.NET", "date": "Sun, 03 Aug 2025 14:08:53 GMT", "content-length": "1233"}, "content_length": 1233, "content_type": "text/html", "server": "Microsoft-IIS/10.0", "response_time": 1.249156, "encoding": "ISO-8859-1", "cookies": {}}}, "security_analysis": {}, "endpoint_discovery": {"common_endpoints": [{"path": "/", "url": "http://rapi.earthlink.iq/", "status_code": 403, "content_length": 1233, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/main", "url": "http://rapi.earthlink.iq/main", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/login", "url": "http://rapi.earthlink.iq/login", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/signin", "url": "http://rapi.earthlink.iq/signin", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/index", "url": "http://rapi.earthlink.iq/index", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/default", "url": "http://rapi.earthlink.iq/default", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/home", "url": "http://rapi.earthlink.iq/home", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/auth", "url": "http://rapi.earthlink.iq/auth", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/authenticate", "url": "http://rapi.earthlink.iq/authenticate", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/administrator", "url": "http://rapi.earthlink.iq/administrator", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/panel", "url": "http://rapi.earthlink.iq/panel", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/admin", "url": "http://rapi.earthlink.iq/admin", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/dashboard", "url": "http://rapi.earthlink.iq/dashboard", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/api", "url": "http://rapi.earthlink.iq/api", "status_code": 200, "content_length": 0, "content_type": "", "accessible": true, "redirect": 302}, {"path": "/api/v1", "url": "http://rapi.earthlink.iq/api/v1", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/api/v2", "url": "http://rapi.earthlink.iq/api/v2", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/rest", "url": "http://rapi.earthlink.iq/rest", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/graphql", "url": "http://rapi.earthlink.iq/graphql", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/swagger", "url": "http://rapi.earthlink.iq/swagger", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/documentation", "url": "http://rapi.earthlink.iq/documentation", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/docs", "url": "http://rapi.earthlink.iq/docs", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/openapi", "url": "http://rapi.earthlink.iq/openapi", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/health", "url": "http://rapi.earthlink.iq/health", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/version", "url": "http://rapi.earthlink.iq/version", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/ping", "url": "http://rapi.earthlink.iq/ping", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/sitemap.xml", "url": "http://rapi.earthlink.iq/sitemap.xml", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/status", "url": "http://rapi.earthlink.iq/status", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/robots.txt", "url": "http://rapi.earthlink.iq/robots.txt", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/.well-known", "url": "http://rapi.earthlink.iq/.well-known", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}], "api_endpoints": [{"path": "/api/subscriptions", "url": "http://rapi.earthlink.iq/api/subscriptions", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/api/users", "url": "http://rapi.earthlink.iq/api/users", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/api/subscribers", "url": "http://rapi.earthlink.iq/api/subscribers", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/api/customers", "url": "http://rapi.earthlink.iq/api/customers", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/api/plans", "url": "http://rapi.earthlink.iq/api/plans", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/api/packages", "url": "http://rapi.earthlink.iq/api/packages", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/api/billing", "url": "http://rapi.earthlink.iq/api/billing", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/api/payments", "url": "http://rapi.earthlink.iq/api/payments", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/api/invoices", "url": "http://rapi.earthlink.iq/api/invoices", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/api/reports", "url": "http://rapi.earthlink.iq/api/reports", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/api/stats", "url": "http://rapi.earthlink.iq/api/stats", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/api/analytics", "url": "http://rapi.earthlink.iq/api/analytics", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/api/auth/login", "url": "http://rapi.earthlink.iq/api/auth/login", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/api/auth/token", "url": "http://rapi.earthlink.iq/api/auth/token", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/api/auth/refresh", "url": "http://rapi.earthlink.iq/api/auth/refresh", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}], "admin_endpoints": [{"path": "/admin/dashboard", "url": "http://rapi.earthlink.iq/admin/dashboard", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/admin/login", "url": "http://rapi.earthlink.iq/admin/login", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/admin/config", "url": "http://rapi.earthlink.iq/admin/config", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/admin/settings", "url": "http://rapi.earthlink.iq/admin/settings", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/reseller/login", "url": "http://rapi.earthlink.iq/reseller/login", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/admin/logs", "url": "http://rapi.earthlink.iq/admin/logs", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/reseller/dashboard", "url": "http://rapi.earthlink.iq/reseller/dashboard", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/admin/users", "url": "http://rapi.earthlink.iq/admin/users", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/reseller", "url": "http://rapi.earthlink.iq/reseller", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/manager", "url": "http://rapi.earthlink.iq/manager", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/cp", "url": "http://rapi.earthlink.iq/cp", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/control", "url": "http://rapi.earthlink.iq/control", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}, {"path": "/cpanel", "url": "http://rapi.earthlink.iq/cpanel", "status_code": 404, "content_length": 1245, "content_type": "text/html", "accessible": false, "redirect": 302}], "discovered_paths": []}, "authentication_analysis": {"login_forms": [], "auth_methods": [{"method": "Basic Authentication", "status_code": 403, "success": false, "response_size": 1233}, {"method": "API Key - X-API-Key", "status_code": 403, "success": false, "response_size": 1233}, {"method": "API Key - Authorization", "status_code": 403, "success": false, "response_size": 1233}, {"method": "API Key - Authorization", "status_code": 403, "success": false, "response_size": 1233}, {"method": "API Key - X-Auth-Token", "status_code": 403, "success": false, "response_size": 1233}], "security_headers": {"headers": {"X-Frame-Options": null, "X-Content-Type-Options": null, "X-XSS-Protection": null, "Strict-Transport-Security": null, "Content-Security-Policy": null, "Referrer-Policy": null, "Permissions-Policy": null}, "security_score": 0}, "session_analysis": {"cookies": {}, "session_cookies": [], "secure_cookies": [], "httponly_cookies": []}}, "data_extraction": {}, "vulnerability_scan": {}}