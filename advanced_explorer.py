#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مستكشف متقدم لإيرثلنك API
=========================
أداة شاملة لفحص واستكشاف موقع إيرثلنك بطرق متعددة
"""

import requests
import socket
import ssl
import urllib3
from urllib.parse import urlparse, urljoin
import json
import time
import logging
from typing import Dict, List, Optional, Any
import base64
import hashlib
import hmac
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import threading
import concurrent.futures

# تعطيل تحذيرات SSL
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('advanced_explorer.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class AdvancedEarthlinkExplorer:
    """مستكشف متقدم لإيرثلنك"""
    
    def __init__(self):
        """تهيئة المستكشف"""
        self.logger = logging.getLogger(__name__)
        self.base_url = "http://rapi.earthlink.iq"
        self.session = requests.Session()
        self.results = {
            "network_analysis": {},
            "security_analysis": {},
            "endpoint_discovery": {},
            "authentication_analysis": {},
            "data_extraction": {},
            "vulnerability_scan": {}
        }
        
        # إعداد session متقدم
        self._setup_session()
    
    def _setup_session(self):
        """إعداد session متقدم"""
        # إعداد retry strategy
        retry_strategy = Retry(
            total=5,
            backoff_factor=2,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # Headers متقدمة
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': '*/*',
            'Accept-Language': 'ar,en-US;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Upgrade-Insecure-Requests': '1'
        })
        
        # تعطيل SSL verification
        self.session.verify = False
        
        # إعداد timeout
        self.session.timeout = 30
    
    def network_analysis(self) -> Dict:
        """تحليل الشبكة والاتصال"""
        self.logger.info("بدء تحليل الشبكة")
        
        analysis = {
            "dns_resolution": {},
            "port_scan": {},
            "ssl_analysis": {},
            "response_analysis": {}
        }
        
        # تحليل DNS
        try:
            parsed_url = urlparse(self.base_url)
            hostname = parsed_url.hostname
            
            # DNS resolution
            ip_addresses = socket.gethostbyname_ex(hostname)
            analysis["dns_resolution"] = {
                "hostname": hostname,
                "ip_addresses": ip_addresses[2],
                "aliases": ip_addresses[1]
            }
            
        except Exception as e:
            analysis["dns_resolution"]["error"] = str(e)
        
        # فحص المنافذ
        common_ports = [80, 443, 8080, 8443, 3000, 5000, 8000, 9000]
        analysis["port_scan"] = self._scan_ports(hostname, common_ports)
        
        # تحليل SSL (إذا كان HTTPS)
        if self.base_url.startswith('https'):
            analysis["ssl_analysis"] = self._analyze_ssl(hostname)
        
        # تحليل الاستجابة
        analysis["response_analysis"] = self._analyze_response()
        
        self.results["network_analysis"] = analysis
        return analysis
    
    def _scan_ports(self, hostname: str, ports: List[int]) -> Dict:
        """فحص المنافذ"""
        open_ports = []
        closed_ports = []
        
        def check_port(port):
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(3)
                result = sock.connect_ex((hostname, port))
                sock.close()
                
                if result == 0:
                    return port, "open"
                else:
                    return port, "closed"
            except:
                return port, "error"
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(check_port, port) for port in ports]
            
            for future in concurrent.futures.as_completed(futures):
                port, status = future.result()
                if status == "open":
                    open_ports.append(port)
                else:
                    closed_ports.append(port)
        
        return {
            "open_ports": open_ports,
            "closed_ports": closed_ports
        }
    
    def _analyze_ssl(self, hostname: str) -> Dict:
        """تحليل SSL"""
        try:
            context = ssl.create_default_context()
            context.check_hostname = False
            context.verify_mode = ssl.CERT_NONE
            
            with socket.create_connection((hostname, 443), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                    cert = ssock.getpeercert()
                    
                    return {
                        "certificate": cert,
                        "cipher": ssock.cipher(),
                        "version": ssock.version()
                    }
        except Exception as e:
            return {"error": str(e)}
    
    def _analyze_response(self) -> Dict:
        """تحليل الاستجابة"""
        try:
            response = self.session.get(self.base_url, timeout=10)
            
            return {
                "status_code": response.status_code,
                "headers": dict(response.headers),
                "content_length": len(response.content),
                "content_type": response.headers.get('content-type', ''),
                "server": response.headers.get('server', ''),
                "response_time": response.elapsed.total_seconds(),
                "encoding": response.encoding,
                "cookies": dict(response.cookies)
            }
        except Exception as e:
            return {"error": str(e)}
    
    def endpoint_discovery(self) -> Dict:
        """اكتشاف نقاط النهاية"""
        self.logger.info("بدء اكتشاف نقاط النهاية")
        
        discovery = {
            "common_endpoints": [],
            "api_endpoints": [],
            "admin_endpoints": [],
            "discovered_paths": []
        }
        
        # قوائم نقاط النهاية المحتملة
        common_paths = [
            '/', '/index', '/home', '/main', '/default',
            '/login', '/signin', '/auth', '/authenticate',
            '/admin', '/administrator', '/panel', '/dashboard',
            '/api', '/api/v1', '/api/v2', '/rest', '/graphql',
            '/docs', '/documentation', '/swagger', '/openapi',
            '/health', '/status', '/ping', '/version',
            '/robots.txt', '/sitemap.xml', '/.well-known'
        ]
        
        api_paths = [
            '/api/users', '/api/subscribers', '/api/customers',
            '/api/subscriptions', '/api/plans', '/api/packages',
            '/api/billing', '/api/payments', '/api/invoices',
            '/api/reports', '/api/stats', '/api/analytics',
            '/api/auth/login', '/api/auth/token', '/api/auth/refresh'
        ]
        
        admin_paths = [
            '/admin/login', '/admin/dashboard', '/admin/users',
            '/admin/settings', '/admin/config', '/admin/logs',
            '/reseller', '/reseller/login', '/reseller/dashboard',
            '/manager', '/control', '/cp', '/cpanel'
        ]
        
        # فحص نقاط النهاية
        discovery["common_endpoints"] = self._check_endpoints(common_paths)
        discovery["api_endpoints"] = self._check_endpoints(api_paths)
        discovery["admin_endpoints"] = self._check_endpoints(admin_paths)
        
        # اكتشاف مسارات إضافية
        discovery["discovered_paths"] = self._discover_additional_paths()
        
        self.results["endpoint_discovery"] = discovery
        return discovery
    
    def _check_endpoints(self, paths: List[str]) -> List[Dict]:
        """فحص قائمة من نقاط النهاية"""
        results = []
        
        def check_endpoint(path):
            try:
                url = urljoin(self.base_url, path)
                response = self.session.get(url, timeout=5)
                
                return {
                    "path": path,
                    "url": url,
                    "status_code": response.status_code,
                    "content_length": len(response.content),
                    "content_type": response.headers.get('content-type', ''),
                    "accessible": response.status_code < 400,
                    "redirect": response.history[0].status_code if response.history else None
                }
            except Exception as e:
                return {
                    "path": path,
                    "url": urljoin(self.base_url, path),
                    "error": str(e),
                    "accessible": False
                }
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(check_endpoint, path) for path in paths]
            
            for future in concurrent.futures.as_completed(futures):
                result = future.result()
                results.append(result)
        
        return results
    
    def _discover_additional_paths(self) -> List[str]:
        """اكتشاف مسارات إضافية من المحتوى"""
        discovered = []
        
        try:
            # فحص الصفحة الرئيسية للروابط
            response = self.session.get(self.base_url)
            content = response.text.lower()
            
            # البحث عن أنماط مختلفة
            import re
            
            # البحث عن روابط
            links = re.findall(r'href=["\']([^"\']+)["\']', content)
            for link in links:
                if link.startswith('/') and link not in discovered:
                    discovered.append(link)
            
            # البحث عن مسارات API
            api_patterns = [
                r'/api/[a-zA-Z0-9_/]+',
                r'/rest/[a-zA-Z0-9_/]+',
                r'/v\d+/[a-zA-Z0-9_/]+'
            ]
            
            for pattern in api_patterns:
                matches = re.findall(pattern, content)
                discovered.extend(matches)
            
        except Exception as e:
            self.logger.warning(f"خطأ في اكتشاف المسارات: {str(e)}")
        
        return list(set(discovered))
    
    def authentication_analysis(self, username: str, password: str) -> Dict:
        """تحليل آليات المصادقة"""
        self.logger.info("بدء تحليل المصادقة")
        
        analysis = {
            "login_forms": [],
            "auth_methods": [],
            "security_headers": {},
            "session_analysis": {}
        }
        
        # تحليل نماذج تسجيل الدخول
        analysis["login_forms"] = self._analyze_login_forms()
        
        # اختبار طرق المصادقة المختلفة
        analysis["auth_methods"] = self._test_auth_methods(username, password)
        
        # تحليل headers الأمان
        analysis["security_headers"] = self._analyze_security_headers()
        
        # تحليل الجلسات
        analysis["session_analysis"] = self._analyze_sessions()
        
        self.results["authentication_analysis"] = analysis
        return analysis
    
    def _analyze_login_forms(self) -> List[Dict]:
        """تحليل نماذج تسجيل الدخول"""
        forms = []
        
        login_pages = [
            '/', '/login', '/signin', '/auth', '/admin', '/admin/login'
        ]
        
        for page in login_pages:
            try:
                url = urljoin(self.base_url, page)
                response = self.session.get(url)
                
                if response.status_code == 200:
                    # تحليل HTML للبحث عن نماذج
                    from bs4 import BeautifulSoup
                    soup = BeautifulSoup(response.text, 'html.parser')
                    
                    page_forms = soup.find_all('form')
                    for form in page_forms:
                        form_info = {
                            "page": page,
                            "action": form.get('action', ''),
                            "method": form.get('method', 'GET'),
                            "inputs": []
                        }
                        
                        inputs = form.find_all('input')
                        for inp in inputs:
                            form_info["inputs"].append({
                                "type": inp.get('type', 'text'),
                                "name": inp.get('name', ''),
                                "id": inp.get('id', ''),
                                "placeholder": inp.get('placeholder', '')
                            })
                        
                        forms.append(form_info)
                        
            except Exception as e:
                self.logger.warning(f"خطأ في تحليل {page}: {str(e)}")
        
        return forms
    
    def _test_auth_methods(self, username: str, password: str) -> List[Dict]:
        """اختبار طرق المصادقة المختلفة"""
        methods = []
        
        # Basic Authentication
        try:
            from requests.auth import HTTPBasicAuth
            response = self.session.get(self.base_url, auth=HTTPBasicAuth(username, password))
            methods.append({
                "method": "Basic Authentication",
                "status_code": response.status_code,
                "success": response.status_code == 200,
                "response_size": len(response.content)
            })
        except Exception as e:
            methods.append({
                "method": "Basic Authentication",
                "error": str(e),
                "success": False
            })
        
        # API Key في Headers
        api_key_headers = [
            {'X-API-Key': password},
            {'Authorization': f'Bearer {password}'},
            {'Authorization': f'Token {password}'},
            {'X-Auth-Token': password}
        ]
        
        for headers in api_key_headers:
            try:
                response = self.session.get(self.base_url, headers=headers)
                methods.append({
                    "method": f"API Key - {list(headers.keys())[0]}",
                    "status_code": response.status_code,
                    "success": response.status_code == 200,
                    "response_size": len(response.content)
                })
            except Exception as e:
                methods.append({
                    "method": f"API Key - {list(headers.keys())[0]}",
                    "error": str(e),
                    "success": False
                })
        
        return methods
    
    def _analyze_security_headers(self) -> Dict:
        """تحليل headers الأمان"""
        try:
            response = self.session.get(self.base_url)
            headers = response.headers
            
            security_headers = {
                "X-Frame-Options": headers.get('X-Frame-Options'),
                "X-Content-Type-Options": headers.get('X-Content-Type-Options'),
                "X-XSS-Protection": headers.get('X-XSS-Protection'),
                "Strict-Transport-Security": headers.get('Strict-Transport-Security'),
                "Content-Security-Policy": headers.get('Content-Security-Policy'),
                "Referrer-Policy": headers.get('Referrer-Policy'),
                "Permissions-Policy": headers.get('Permissions-Policy')
            }
            
            return {
                "headers": security_headers,
                "security_score": sum(1 for v in security_headers.values() if v is not None)
            }
        except Exception as e:
            return {"error": str(e)}
    
    def _analyze_sessions(self) -> Dict:
        """تحليل الجلسات"""
        try:
            response = self.session.get(self.base_url)
            
            return {
                "cookies": dict(response.cookies),
                "session_cookies": [name for name in response.cookies.keys() 
                                  if 'session' in name.lower() or 'sid' in name.lower()],
                "secure_cookies": [name for name, cookie in response.cookies.items() 
                                 if hasattr(cookie, 'secure') and cookie.secure],
                "httponly_cookies": [name for name, cookie in response.cookies.items() 
                                   if hasattr(cookie, 'has_nonstandard_attr') and 
                                   cookie.has_nonstandard_attr('HttpOnly')]
            }
        except Exception as e:
            return {"error": str(e)}
    
    def comprehensive_scan(self, username: str, password: str) -> Dict:
        """فحص شامل للموقع"""
        self.logger.info("بدء الفحص الشامل")
        
        print("🔍 بدء الفحص الشامل لموقع إيرثلنك")
        print("=" * 50)
        
        # تحليل الشبكة
        print("📡 تحليل الشبكة...")
        self.network_analysis()
        
        # اكتشاف نقاط النهاية
        print("🔎 اكتشاف نقاط النهاية...")
        self.endpoint_discovery()
        
        # تحليل المصادقة
        print("🔐 تحليل المصادقة...")
        self.authentication_analysis(username, password)
        
        # حفظ النتائج
        with open('comprehensive_scan_results.json', 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        print("💾 تم حفظ نتائج الفحص في comprehensive_scan_results.json")
        
        return self.results
    
    def generate_report(self) -> str:
        """إنشاء تقرير مفصل"""
        report = []
        report.append("📊 تقرير فحص موقع إيرثلنك")
        report.append("=" * 50)
        
        # تحليل الشبكة
        if "network_analysis" in self.results:
            network = self.results["network_analysis"]
            report.append("\n🌐 تحليل الشبكة:")
            
            if "dns_resolution" in network:
                dns = network["dns_resolution"]
                if "ip_addresses" in dns:
                    report.append(f"  - عناوين IP: {', '.join(dns['ip_addresses'])}")
            
            if "port_scan" in network:
                ports = network["port_scan"]
                if "open_ports" in ports:
                    report.append(f"  - المنافذ المفتوحة: {', '.join(map(str, ports['open_ports']))}")
            
            if "response_analysis" in network:
                response = network["response_analysis"]
                if "status_code" in response:
                    report.append(f"  - رمز الاستجابة: {response['status_code']}")
                if "server" in response:
                    report.append(f"  - الخادم: {response['server']}")
        
        # اكتشاف نقاط النهاية
        if "endpoint_discovery" in self.results:
            endpoints = self.results["endpoint_discovery"]
            report.append("\n🔍 نقاط النهاية المكتشفة:")
            
            for category in ["common_endpoints", "api_endpoints", "admin_endpoints"]:
                if category in endpoints:
                    accessible = [ep for ep in endpoints[category] if ep.get("accessible")]
                    if accessible:
                        report.append(f"  - {category}: {len(accessible)} نقطة متاحة")
        
        # تحليل المصادقة
        if "authentication_analysis" in self.results:
            auth = self.results["authentication_analysis"]
            report.append("\n🔐 تحليل المصادقة:")
            
            if "login_forms" in auth:
                report.append(f"  - نماذج تسجيل الدخول: {len(auth['login_forms'])}")
            
            if "auth_methods" in auth:
                successful_methods = [m for m in auth["auth_methods"] if m.get("success")]
                report.append(f"  - طرق المصادقة الناجحة: {len(successful_methods)}")
        
        return "\n".join(report)


def main():
    """الدالة الرئيسية"""
    print("🔍 مستكشف إيرثلنك المتقدم")
    print("=" * 50)
    
    # بيانات تسجيل الدخول
    username = "admin@adhamm1"
    password = "adham12398071@@11"
    
    # إنشاء المستكشف
    explorer = AdvancedEarthlinkExplorer()
    
    # تشغيل الفحص الشامل
    results = explorer.comprehensive_scan(username, password)
    
    # إنشاء التقرير
    report = explorer.generate_report()
    print("\n" + report)
    
    # حفظ التقرير
    with open('scan_report.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print("\n📄 تم حفظ التقرير في scan_report.txt")


if __name__ == "__main__":
    main()
