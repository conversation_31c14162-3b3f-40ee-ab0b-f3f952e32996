#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مستكشف API إيرثلنك المتخصص
============================
أداة متخصصة لاستكشاف نقطة /api المكتشفة
"""

import requests
import json
import time
import logging
from typing import Dict, List, Optional, Any
import urllib3
from urllib.parse import urljoin
import base64
import hashlib
import hmac
from requests.auth import HTTPBasicAuth, HTTPDigestAuth
import itertools

# تعطيل تحذيرات SSL
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('api_explorer.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class EarthlinkAPIExplorer:
    """مستكشف API إيرثلنك المتخصص"""
    
    def __init__(self):
        """تهيئة المستكشف"""
        self.logger = logging.getLogger(__name__)
        self.base_url = "http://rapi.earthlink.iq"
        self.api_endpoint = f"{self.base_url}/api"
        self.session = requests.Session()
        self.results = {
            "api_discovery": {},
            "authentication_tests": {},
            "endpoint_mapping": {},
            "data_extraction": {},
            "successful_requests": []
        }
        
        self._setup_session()
    
    def _setup_session(self):
        """إعداد session متقدم"""
        # Headers متنوعة
        self.session.headers.update({
            'User-Agent': 'EarthlinkAPIClient/1.0',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'ar,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Content-Type': 'application/json'
        })
        
        self.session.verify = False
        self.session.timeout = 15
    
    def explore_api_endpoint(self, username: str, password: str) -> Dict:
        """استكشاف شامل لنقطة API"""
        self.logger.info("بدء استكشاف نقطة API")
        
        print("🔍 استكشاف نقطة API إيرثلنك")
        print("=" * 50)
        
        # اختبار نقطة API الأساسية
        print("📡 اختبار نقطة API الأساسية...")
        self._test_basic_api_endpoint()
        
        # اختبار طرق HTTP مختلفة
        print("🔧 اختبار طرق HTTP مختلفة...")
        self._test_http_methods(username, password)
        
        # اختبار المصادقة المتقدمة
        print("🔐 اختبار طرق المصادقة المتقدمة...")
        self._test_advanced_authentication(username, password)
        
        # اختبار نقاط فرعية
        print("🗂️ اختبار نقاط فرعية...")
        self._test_sub_endpoints(username, password)
        
        # اختبار معاملات مختلفة
        print("⚙️ اختبار معاملات مختلفة...")
        self._test_parameters(username, password)
        
        # محاولة استخراج البيانات
        print("📊 محاولة استخراج البيانات...")
        self._attempt_data_extraction(username, password)
        
        return self.results
    
    def _test_basic_api_endpoint(self):
        """اختبار نقطة API الأساسية"""
        try:
            response = self.session.get(self.api_endpoint)
            
            self.results["api_discovery"]["basic_test"] = {
                "url": self.api_endpoint,
                "status_code": response.status_code,
                "headers": dict(response.headers),
                "content_length": len(response.content),
                "content": response.text[:500] if response.text else "",
                "response_time": response.elapsed.total_seconds()
            }
            
            print(f"  - رمز الاستجابة: {response.status_code}")
            print(f"  - طول المحتوى: {len(response.content)} بايت")
            
        except Exception as e:
            self.results["api_discovery"]["basic_test"] = {"error": str(e)}
            print(f"  - خطأ: {str(e)}")
    
    def _test_http_methods(self, username: str, password: str):
        """اختبار طرق HTTP مختلفة"""
        methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD']
        
        self.results["api_discovery"]["http_methods"] = {}
        
        for method in methods:
            try:
                # اختبار بدون مصادقة
                response = self.session.request(method, self.api_endpoint)
                
                method_result = {
                    "status_code": response.status_code,
                    "headers": dict(response.headers),
                    "content_length": len(response.content),
                    "allowed": response.status_code not in [405, 501]
                }
                
                # اختبار مع Basic Auth
                if username and password:
                    auth_response = self.session.request(
                        method, 
                        self.api_endpoint, 
                        auth=HTTPBasicAuth(username, password)
                    )
                    
                    method_result["with_auth"] = {
                        "status_code": auth_response.status_code,
                        "content_length": len(auth_response.content),
                        "different_response": auth_response.status_code != response.status_code
                    }
                
                self.results["api_discovery"]["http_methods"][method] = method_result
                
                print(f"  - {method}: {response.status_code}")
                
            except Exception as e:
                self.results["api_discovery"]["http_methods"][method] = {"error": str(e)}
                print(f"  - {method}: خطأ - {str(e)}")
    
    def _test_advanced_authentication(self, username: str, password: str):
        """اختبار طرق المصادقة المتقدمة"""
        auth_tests = []
        
        # Basic Authentication
        try:
            response = self.session.get(self.api_endpoint, auth=HTTPBasicAuth(username, password))
            auth_tests.append({
                "method": "Basic Auth",
                "status_code": response.status_code,
                "success": response.status_code == 200,
                "content_preview": response.text[:200]
            })
        except Exception as e:
            auth_tests.append({"method": "Basic Auth", "error": str(e)})
        
        # Digest Authentication
        try:
            response = self.session.get(self.api_endpoint, auth=HTTPDigestAuth(username, password))
            auth_tests.append({
                "method": "Digest Auth",
                "status_code": response.status_code,
                "success": response.status_code == 200,
                "content_preview": response.text[:200]
            })
        except Exception as e:
            auth_tests.append({"method": "Digest Auth", "error": str(e)})
        
        # API Key في Headers مختلفة
        api_key_headers = [
            {'X-API-Key': password},
            {'Authorization': f'Bearer {password}'},
            {'Authorization': f'Token {password}'},
            {'Authorization': f'ApiKey {password}'},
            {'X-Auth-Token': password},
            {'X-Access-Token': password},
            {'API-Key': password},
            {'Token': password}
        ]
        
        for headers in api_key_headers:
            try:
                response = self.session.get(self.api_endpoint, headers=headers)
                auth_tests.append({
                    "method": f"API Key - {list(headers.keys())[0]}",
                    "status_code": response.status_code,
                    "success": response.status_code == 200,
                    "content_preview": response.text[:200]
                })
            except Exception as e:
                auth_tests.append({
                    "method": f"API Key - {list(headers.keys())[0]}",
                    "error": str(e)
                })
        
        # POST مع بيانات تسجيل الدخول
        login_data_formats = [
            {"username": username, "password": password},
            {"user": username, "pass": password},
            {"email": username, "password": password},
            {"login": username, "password": password},
            {"admin": username, "password": password}
        ]
        
        for data in login_data_formats:
            try:
                # JSON
                response = self.session.post(self.api_endpoint, json=data)
                auth_tests.append({
                    "method": f"POST JSON - {list(data.keys())[0]}",
                    "status_code": response.status_code,
                    "success": response.status_code == 200,
                    "content_preview": response.text[:200]
                })
                
                # Form data
                response = self.session.post(self.api_endpoint, data=data)
                auth_tests.append({
                    "method": f"POST Form - {list(data.keys())[0]}",
                    "status_code": response.status_code,
                    "success": response.status_code == 200,
                    "content_preview": response.text[:200]
                })
                
            except Exception as e:
                auth_tests.append({
                    "method": f"POST - {list(data.keys())[0]}",
                    "error": str(e)
                })
        
        self.results["authentication_tests"] = auth_tests
        
        # طباعة النتائج الناجحة
        successful_auths = [test for test in auth_tests if test.get("success")]
        if successful_auths:
            print(f"  ✅ وجدت {len(successful_auths)} طريقة مصادقة ناجحة!")
            for auth in successful_auths:
                print(f"    - {auth['method']}")
        else:
            print("  ❌ لم تنجح أي طريقة مصادقة")
    
    def _test_sub_endpoints(self, username: str, password: str):
        """اختبار نقاط فرعية تحت /api"""
        sub_endpoints = [
            '', '/login', '/auth', '/authenticate', '/token',
            '/users', '/subscribers', '/customers', '/clients',
            '/subscriptions', '/plans', '/packages', '/services',
            '/billing', '/payments', '/invoices', '/transactions',
            '/reports', '/stats', '/analytics', '/dashboard',
            '/admin', '/reseller', '/manager',
            '/v1', '/v2', '/v3',
            '/status', '/health', '/ping', '/version',
            '/docs', '/swagger', '/openapi',
            '/test', '/demo', '/sample'
        ]
        
        endpoint_results = []
        
        for endpoint in sub_endpoints:
            url = f"{self.api_endpoint}{endpoint}"
            
            try:
                # اختبار GET
                response = self.session.get(url)
                result = {
                    "endpoint": endpoint,
                    "url": url,
                    "method": "GET",
                    "status_code": response.status_code,
                    "content_length": len(response.content),
                    "accessible": response.status_code < 400,
                    "content_type": response.headers.get('content-type', ''),
                    "content_preview": response.text[:100] if response.text else ""
                }
                
                # اختبار مع مصادقة إذا كان متاحاً
                if username and password:
                    auth_response = self.session.get(url, auth=HTTPBasicAuth(username, password))
                    if auth_response.status_code != response.status_code:
                        result["auth_different"] = True
                        result["auth_status_code"] = auth_response.status_code
                        result["auth_content_preview"] = auth_response.text[:100]
                
                endpoint_results.append(result)
                
                if result["accessible"]:
                    print(f"  ✅ {endpoint or '/'}: {response.status_code}")
                
            except Exception as e:
                endpoint_results.append({
                    "endpoint": endpoint,
                    "url": url,
                    "error": str(e)
                })
        
        self.results["endpoint_mapping"] = endpoint_results
        
        # عرض النقاط المتاحة
        accessible_endpoints = [ep for ep in endpoint_results if ep.get("accessible")]
        print(f"  📊 وجدت {len(accessible_endpoints)} نقطة فرعية متاحة")
    
    def _test_parameters(self, username: str, password: str):
        """اختبار معاملات مختلفة"""
        parameter_tests = []
        
        # معاملات GET
        get_params = [
            {'action': 'login'},
            {'method': 'authenticate'},
            {'cmd': 'auth'},
            {'function': 'login'},
            {'op': 'signin'},
            {'task': 'auth'},
            {'mode': 'login'},
            {'type': 'auth'},
            {'format': 'json'},
            {'output': 'json'},
            {'response': 'json'},
            {'api_key': password},
            {'token': password},
            {'key': password},
            {'username': username, 'password': password},
            {'user': username, 'pass': password}
        ]
        
        for params in get_params:
            try:
                response = self.session.get(self.api_endpoint, params=params)
                parameter_tests.append({
                    "type": "GET params",
                    "params": params,
                    "status_code": response.status_code,
                    "content_length": len(response.content),
                    "success": response.status_code == 200,
                    "content_preview": response.text[:100]
                })
                
                if response.status_code == 200:
                    print(f"  ✅ معامل ناجح: {list(params.keys())[0]}")
                    
            except Exception as e:
                parameter_tests.append({
                    "type": "GET params",
                    "params": params,
                    "error": str(e)
                })
        
        self.results["parameter_tests"] = parameter_tests
    
    def _attempt_data_extraction(self, username: str, password: str):
        """محاولة استخراج البيانات"""
        extraction_attempts = []
        
        # جمع جميع الطلبات الناجحة
        successful_requests = []
        
        # من اختبارات المصادقة
        for test in self.results.get("authentication_tests", []):
            if test.get("success"):
                successful_requests.append(test)
        
        # من اختبارات النقاط الفرعية
        for endpoint in self.results.get("endpoint_mapping", []):
            if endpoint.get("accessible"):
                successful_requests.append(endpoint)
        
        # من اختبارات المعاملات
        for param_test in self.results.get("parameter_tests", []):
            if param_test.get("success"):
                successful_requests.append(param_test)
        
        self.results["successful_requests"] = successful_requests
        
        # محاولة استخراج بيانات من الطلبات الناجحة
        for request in successful_requests:
            if "content_preview" in request and request["content_preview"]:
                try:
                    # محاولة تحليل JSON
                    if request["content_preview"].strip().startswith('{'):
                        json_data = json.loads(request["content_preview"])
                        extraction_attempts.append({
                            "source": request.get("method", "unknown"),
                            "type": "JSON",
                            "data": json_data
                        })
                    
                    # البحث عن كلمات مفتاحية
                    content = request["content_preview"].lower()
                    keywords = ['user', 'subscriber', 'customer', 'subscription', 'plan', 'billing']
                    found_keywords = [kw for kw in keywords if kw in content]
                    
                    if found_keywords:
                        extraction_attempts.append({
                            "source": request.get("method", "unknown"),
                            "type": "Keywords",
                            "keywords": found_keywords,
                            "content": request["content_preview"]
                        })
                        
                except Exception as e:
                    pass
        
        self.results["data_extraction"] = extraction_attempts
        
        if extraction_attempts:
            print(f"  📊 استخرجت {len(extraction_attempts)} عينة بيانات")
        else:
            print("  ❌ لم أتمكن من استخراج بيانات")
    
    def generate_detailed_report(self) -> str:
        """إنشاء تقرير مفصل"""
        report = []
        report.append("📊 تقرير استكشاف API إيرثلنك المفصل")
        report.append("=" * 60)
        
        # ملخص عام
        report.append("\n🎯 ملخص عام:")
        report.append(f"  - نقطة API الأساسية: {self.api_endpoint}")
        
        basic_test = self.results.get("api_discovery", {}).get("basic_test", {})
        if "status_code" in basic_test:
            report.append(f"  - رمز استجابة API: {basic_test['status_code']}")
        
        # طرق HTTP
        http_methods = self.results.get("api_discovery", {}).get("http_methods", {})
        allowed_methods = [method for method, data in http_methods.items() 
                          if data.get("allowed", False)]
        if allowed_methods:
            report.append(f"  - طرق HTTP المدعومة: {', '.join(allowed_methods)}")
        
        # المصادقة
        auth_tests = self.results.get("authentication_tests", [])
        successful_auths = [test for test in auth_tests if test.get("success")]
        report.append(f"  - طرق المصادقة الناجحة: {len(successful_auths)}")
        
        for auth in successful_auths:
            report.append(f"    ✅ {auth['method']}")
        
        # النقاط الفرعية
        endpoints = self.results.get("endpoint_mapping", [])
        accessible_endpoints = [ep for ep in endpoints if ep.get("accessible")]
        report.append(f"  - النقاط الفرعية المتاحة: {len(accessible_endpoints)}")
        
        for endpoint in accessible_endpoints:
            report.append(f"    📁 {endpoint['endpoint'] or '/'}: {endpoint['status_code']}")
        
        # البيانات المستخرجة
        extractions = self.results.get("data_extraction", [])
        if extractions:
            report.append(f"\n📊 البيانات المستخرجة ({len(extractions)} عينة):")
            for extraction in extractions:
                report.append(f"  - {extraction['type']} من {extraction['source']}")
        
        # الطلبات الناجحة
        successful = self.results.get("successful_requests", [])
        if successful:
            report.append(f"\n✅ الطلبات الناجحة ({len(successful)}):")
            for req in successful[:5]:  # أول 5 فقط
                method = req.get("method", req.get("type", "unknown"))
                report.append(f"  - {method}")
        
        return "\n".join(report)


def main():
    """الدالة الرئيسية"""
    print("🔍 مستكشف API إيرثلنك المتخصص")
    print("=" * 50)
    
    # بيانات تسجيل الدخول
    username = "admin@adhamm1"
    password = "adham12398071@@11"
    
    # إنشاء المستكشف
    explorer = EarthlinkAPIExplorer()
    
    # تشغيل الاستكشاف
    results = explorer.explore_api_endpoint(username, password)
    
    # حفظ النتائج
    with open('api_exploration_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print("\n💾 تم حفظ النتائج في api_exploration_results.json")
    
    # إنشاء التقرير
    report = explorer.generate_detailed_report()
    print("\n" + report)
    
    # حفظ التقرير
    with open('api_detailed_report.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print("\n📄 تم حفظ التقرير في api_detailed_report.txt")


if __name__ == "__main__":
    main()
