#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مستكشف إيرثلنك باستخدام Selenium
==================================
أداة لاستكشاف موقع إيرثلنك باستخدام متصفح آلي
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
import time
import json
import logging
from typing import Dict, List, Optional
import os

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('selenium_explorer.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class EarthlinkSeleniumExplorer:
    """مستكشف إيرثلنك باستخدام Selenium"""
    
    def __init__(self, headless: bool = False):
        """
        تهيئة المستكشف
        
        Args:
            headless: تشغيل المتصفح في الخلفية
        """
        self.logger = logging.getLogger(__name__)
        self.driver = None
        self.wait = None
        self.headless = headless
        self.base_url = "http://rapi.earthlink.iq"
        
    def setup_driver(self):
        """إعداد متصفح Chrome"""
        try:
            chrome_options = Options()
            
            if self.headless:
                chrome_options.add_argument("--headless")
            
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
            chrome_options.add_argument("--accept-lang=ar,en")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            self.wait = WebDriverWait(self.driver, 10)
            
            self.logger.info("تم إعداد متصفح Chrome بنجاح")
            return True
            
        except Exception as e:
            self.logger.error(f"فشل في إعداد المتصفح: {str(e)}")
            return False
    
    def explore_site(self, username: str, password: str) -> Dict:
        """
        استكشاف الموقع ومحاولة تسجيل الدخول
        
        Args:
            username: اسم المستخدم
            password: كلمة المرور
            
        Returns:
            معلومات مستكشفة عن الموقع
        """
        if not self.setup_driver():
            return {"error": "فشل في إعداد المتصفح"}
        
        results = {
            "site_info": {},
            "login_attempts": [],
            "discovered_pages": [],
            "forms_found": [],
            "data_extracted": {},
            "screenshots": []
        }
        
        try:
            # زيارة الموقع الرئيسي
            self.logger.info(f"زيارة الموقع: {self.base_url}")
            self.driver.get(self.base_url)
            time.sleep(3)
            
            # أخذ لقطة شاشة
            screenshot_path = "screenshot_main.png"
            self.driver.save_screenshot(screenshot_path)
            results["screenshots"].append(screenshot_path)
            
            # جمع معلومات الصفحة
            results["site_info"] = {
                "title": self.driver.title,
                "url": self.driver.current_url,
                "page_source_length": len(self.driver.page_source)
            }
            
            # البحث عن نماذج تسجيل الدخول
            login_forms = self.find_login_forms()
            results["forms_found"] = login_forms
            
            # محاولة تسجيل الدخول
            login_success = False
            for form_info in login_forms:
                login_result = self.attempt_login(form_info, username, password)
                results["login_attempts"].append(login_result)
                
                if login_result.get("success"):
                    login_success = True
                    break
            
            # إذا نجح تسجيل الدخول، استكشف الصفحات الداخلية
            if login_success:
                self.logger.info("نجح تسجيل الدخول، بدء استكشاف الصفحات الداخلية")
                results["discovered_pages"] = self.explore_internal_pages()
                results["data_extracted"] = self.extract_data()
            
            # محاولة الوصول لصفحات مختلفة
            else:
                self.logger.info("فشل تسجيل الدخول، محاولة الوصول لصفحات مختلفة")
                results["discovered_pages"] = self.try_different_urls()
            
        except Exception as e:
            self.logger.error(f"خطأ في استكشاف الموقع: {str(e)}")
            results["error"] = str(e)
        
        finally:
            if self.driver:
                self.driver.quit()
        
        return results
    
    def find_login_forms(self) -> List[Dict]:
        """البحث عن نماذج تسجيل الدخول"""
        forms = []
        
        try:
            # البحث عن جميع النماذج
            form_elements = self.driver.find_elements(By.TAG_NAME, "form")
            
            for i, form in enumerate(form_elements):
                form_info = {
                    "form_index": i,
                    "action": form.get_attribute("action") or "",
                    "method": form.get_attribute("method") or "GET",
                    "inputs": []
                }
                
                # البحث عن حقول الإدخال
                inputs = form.find_elements(By.TAG_NAME, "input")
                for input_elem in inputs:
                    input_info = {
                        "type": input_elem.get_attribute("type") or "text",
                        "name": input_elem.get_attribute("name") or "",
                        "id": input_elem.get_attribute("id") or "",
                        "placeholder": input_elem.get_attribute("placeholder") or "",
                        "class": input_elem.get_attribute("class") or ""
                    }
                    form_info["inputs"].append(input_info)
                
                forms.append(form_info)
            
            # البحث عن حقول تسجيل الدخول المحتملة خارج النماذج
            username_selectors = [
                "input[type='text']", "input[type='email']", "input[name*='user']",
                "input[name*='login']", "input[name*='email']", "input[id*='user']",
                "input[id*='login']", "input[id*='email']"
            ]
            
            password_selectors = [
                "input[type='password']", "input[name*='pass']", "input[id*='pass']"
            ]
            
            # إضافة معلومات عن حقول منفصلة
            standalone_fields = {
                "username_fields": [],
                "password_fields": []
            }
            
            for selector in username_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for elem in elements:
                        standalone_fields["username_fields"].append({
                            "selector": selector,
                            "name": elem.get_attribute("name"),
                            "id": elem.get_attribute("id")
                        })
                except:
                    pass
            
            for selector in password_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for elem in elements:
                        standalone_fields["password_fields"].append({
                            "selector": selector,
                            "name": elem.get_attribute("name"),
                            "id": elem.get_attribute("id")
                        })
                except:
                    pass
            
            if standalone_fields["username_fields"] or standalone_fields["password_fields"]:
                forms.append({
                    "form_index": -1,
                    "type": "standalone_fields",
                    "fields": standalone_fields
                })
            
        except Exception as e:
            self.logger.error(f"خطأ في البحث عن نماذج تسجيل الدخول: {str(e)}")
        
        return forms
    
    def attempt_login(self, form_info: Dict, username: str, password: str) -> Dict:
        """محاولة تسجيل الدخول باستخدام نموذج معين"""
        result = {
            "form_info": form_info,
            "success": False,
            "error": None,
            "screenshot": None
        }
        
        try:
            # إذا كان نموذج عادي
            if form_info.get("form_index", -1) >= 0:
                form = self.driver.find_elements(By.TAG_NAME, "form")[form_info["form_index"]]
                
                # البحث عن حقول اسم المستخدم وكلمة المرور
                username_field = None
                password_field = None
                
                for input_info in form_info.get("inputs", []):
                    input_elem = None
                    
                    # محاولة العثور على العنصر
                    if input_info["name"]:
                        try:
                            input_elem = form.find_element(By.NAME, input_info["name"])
                        except:
                            pass
                    
                    if not input_elem and input_info["id"]:
                        try:
                            input_elem = form.find_element(By.ID, input_info["id"])
                        except:
                            pass
                    
                    if input_elem:
                        input_type = input_info["type"].lower()
                        
                        # تحديد نوع الحقل
                        if (input_type in ["text", "email"] or 
                            "user" in input_info["name"].lower() or 
                            "login" in input_info["name"].lower() or
                            "email" in input_info["name"].lower()):
                            username_field = input_elem
                        
                        elif input_type == "password":
                            password_field = input_elem
                
                # إدخال البيانات
                if username_field and password_field:
                    username_field.clear()
                    username_field.send_keys(username)
                    
                    password_field.clear()
                    password_field.send_keys(password)
                    
                    # البحث عن زر الإرسال
                    submit_button = None
                    try:
                        submit_button = form.find_element(By.CSS_SELECTOR, "input[type='submit'], button[type='submit'], button")
                    except:
                        pass
                    
                    # إرسال النموذج
                    if submit_button:
                        submit_button.click()
                    else:
                        password_field.send_keys(Keys.RETURN)
                    
                    time.sleep(3)
                    
                    # فحص نجاح تسجيل الدخول
                    current_url = self.driver.current_url
                    page_source = self.driver.page_source.lower()
                    
                    success_indicators = [
                        "dashboard", "welcome", "logout", "admin", "panel",
                        "لوحة", "مرحبا", "خروج", "إدارة"
                    ]
                    
                    if (current_url != self.base_url or 
                        any(indicator in page_source for indicator in success_indicators)):
                        result["success"] = True
                        
                        # أخذ لقطة شاشة للنجاح
                        screenshot_path = f"screenshot_login_success_{int(time.time())}.png"
                        self.driver.save_screenshot(screenshot_path)
                        result["screenshot"] = screenshot_path
            
            # إذا كانت حقول منفصلة
            elif form_info.get("type") == "standalone_fields":
                fields = form_info.get("fields", {})
                
                # محاولة ملء الحقول المنفصلة
                if fields.get("username_fields") and fields.get("password_fields"):
                    username_field = None
                    password_field = None
                    
                    # العثور على حقل اسم المستخدم
                    for field_info in fields["username_fields"]:
                        try:
                            if field_info["id"]:
                                username_field = self.driver.find_element(By.ID, field_info["id"])
                            elif field_info["name"]:
                                username_field = self.driver.find_element(By.NAME, field_info["name"])
                            
                            if username_field:
                                break
                        except:
                            continue
                    
                    # العثور على حقل كلمة المرور
                    for field_info in fields["password_fields"]:
                        try:
                            if field_info["id"]:
                                password_field = self.driver.find_element(By.ID, field_info["id"])
                            elif field_info["name"]:
                                password_field = self.driver.find_element(By.NAME, field_info["name"])
                            
                            if password_field:
                                break
                        except:
                            continue
                    
                    # إدخال البيانات
                    if username_field and password_field:
                        username_field.clear()
                        username_field.send_keys(username)
                        
                        password_field.clear()
                        password_field.send_keys(password)
                        password_field.send_keys(Keys.RETURN)
                        
                        time.sleep(3)
                        
                        # فحص النجاح
                        current_url = self.driver.current_url
                        if current_url != self.base_url:
                            result["success"] = True
                            
                            screenshot_path = f"screenshot_standalone_success_{int(time.time())}.png"
                            self.driver.save_screenshot(screenshot_path)
                            result["screenshot"] = screenshot_path
        
        except Exception as e:
            result["error"] = str(e)
            self.logger.error(f"خطأ في محاولة تسجيل الدخول: {str(e)}")
        
        return result
    
    def explore_internal_pages(self) -> List[Dict]:
        """استكشاف الصفحات الداخلية بعد تسجيل الدخول"""
        pages = []
        
        # قائمة بالروابط المحتملة
        potential_links = [
            "/dashboard", "/admin", "/panel", "/users", "/subscribers",
            "/customers", "/subscriptions", "/plans", "/billing",
            "/reports", "/settings", "/profile"
        ]
        
        for link in potential_links:
            try:
                full_url = self.base_url + link
                self.driver.get(full_url)
                time.sleep(2)
                
                page_info = {
                    "url": full_url,
                    "title": self.driver.title,
                    "status": "accessible" if "404" not in self.driver.page_source else "not_found",
                    "content_length": len(self.driver.page_source)
                }
                
                pages.append(page_info)
                
            except Exception as e:
                self.logger.warning(f"فشل في الوصول إلى {link}: {str(e)}")
        
        return pages
    
    def try_different_urls(self) -> List[Dict]:
        """محاولة الوصول لصفحات مختلفة بدون تسجيل دخول"""
        pages = []
        
        urls_to_try = [
            self.base_url + "/login",
            self.base_url + "/admin",
            self.base_url + "/api",
            self.base_url + "/docs",
            self.base_url + "/help",
            self.base_url.replace("rapi", "api"),
            self.base_url.replace("http://", "https://")
        ]
        
        for url in urls_to_try:
            try:
                self.driver.get(url)
                time.sleep(2)
                
                page_info = {
                    "url": url,
                    "title": self.driver.title,
                    "accessible": True,
                    "content_preview": self.driver.page_source[:500]
                }
                
                pages.append(page_info)
                
            except Exception as e:
                pages.append({
                    "url": url,
                    "accessible": False,
                    "error": str(e)
                })
        
        return pages
    
    def extract_data(self) -> Dict:
        """استخراج البيانات من الصفحات"""
        data = {
            "tables": [],
            "lists": [],
            "forms": [],
            "links": []
        }
        
        try:
            # استخراج الجداول
            tables = self.driver.find_elements(By.TAG_NAME, "table")
            for i, table in enumerate(tables):
                table_data = {
                    "index": i,
                    "rows": len(table.find_elements(By.TAG_NAME, "tr")),
                    "columns": len(table.find_elements(By.TAG_NAME, "th")) or len(table.find_elements(By.TAG_NAME, "td")),
                    "text_preview": table.text[:200]
                }
                data["tables"].append(table_data)
            
            # استخراج القوائم
            lists = self.driver.find_elements(By.TAG_NAME, "ul")
            for i, ul in enumerate(lists):
                list_data = {
                    "index": i,
                    "items": len(ul.find_elements(By.TAG_NAME, "li")),
                    "text_preview": ul.text[:200]
                }
                data["lists"].append(list_data)
            
            # استخراج الروابط
            links = self.driver.find_elements(By.TAG_NAME, "a")
            for link in links[:20]:  # أول 20 رابط فقط
                href = link.get_attribute("href")
                if href:
                    data["links"].append({
                        "href": href,
                        "text": link.text[:50]
                    })
        
        except Exception as e:
            self.logger.error(f"خطأ في استخراج البيانات: {str(e)}")
        
        return data


def main():
    """الدالة الرئيسية"""
    print("🔍 مستكشف إيرثلنك باستخدام Selenium")
    print("=" * 50)
    
    # بيانات تسجيل الدخول
    username = "admin@adhamm1"
    password = "adham12398071@@11"
    
    # إنشاء المستكشف
    explorer = EarthlinkSeleniumExplorer(headless=False)  # تغيير إلى True لتشغيل في الخلفية
    
    # بدء الاستكشاف
    print("🚀 بدء استكشاف الموقع...")
    results = explorer.explore_site(username, password)
    
    # حفظ النتائج
    with open('selenium_exploration_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print("💾 تم حفظ نتائج الاستكشاف في selenium_exploration_results.json")
    
    # طباعة ملخص النتائج
    print("\n📊 ملخص النتائج:")
    print(f"- عنوان الموقع: {results.get('site_info', {}).get('title', 'غير متاح')}")
    print(f"- عدد النماذج المكتشفة: {len(results.get('forms_found', []))}")
    print(f"- عدد محاولات تسجيل الدخول: {len(results.get('login_attempts', []))}")
    print(f"- عدد الصفحات المكتشفة: {len(results.get('discovered_pages', []))}")
    
    # فحص نجاح تسجيل الدخول
    login_success = any(attempt.get('success', False) for attempt in results.get('login_attempts', []))
    print(f"- حالة تسجيل الدخول: {'✅ نجح' if login_success else '❌ فشل'}")


if __name__ == "__main__":
    main()
