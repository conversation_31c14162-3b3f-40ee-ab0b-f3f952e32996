{"api_discovery": {"basic_test": {"url": "http://rapi.earthlink.iq/api", "status_code": 200, "headers": {"server": "Microsoft-IIS/10.0", "x-powered-by": "ASP.NET", "date": "Sun, 03 Aug 2025 14:10:43 GMT", "content-length": "0"}, "content_length": 0, "content": "", "response_time": 0.094622}, "http_methods": {"GET": {"status_code": 200, "headers": {"server": "Microsoft-IIS/10.0", "x-powered-by": "ASP.NET", "date": "Sun, 03 Aug 2025 14:10:43 GMT", "content-length": "0"}, "content_length": 0, "allowed": true, "with_auth": {"status_code": 200, "content_length": 0, "different_response": false}}, "POST": {"status_code": 200, "headers": {"server": "Microsoft-IIS/10.0", "x-powered-by": "ASP.NET", "date": "Sun, 03 Aug 2025 14:10:43 GMT", "content-length": "0"}, "content_length": 0, "allowed": true, "with_auth": {"status_code": 200, "content_length": 0, "different_response": false}}, "PUT": {"status_code": 200, "headers": {"server": "Microsoft-IIS/10.0", "x-powered-by": "ASP.NET", "date": "Sun, 03 Aug 2025 14:10:43 GMT", "content-length": "0"}, "content_length": 0, "allowed": true, "with_auth": {"status_code": 200, "content_length": 0, "different_response": false}}, "DELETE": {"status_code": 200, "headers": {"server": "Microsoft-IIS/10.0", "x-powered-by": "ASP.NET", "date": "Sun, 03 Aug 2025 14:10:43 GMT", "content-length": "0"}, "content_length": 0, "allowed": true, "with_auth": {"status_code": 200, "content_length": 0, "different_response": false}}, "PATCH": {"status_code": 200, "headers": {"server": "Microsoft-IIS/10.0", "x-powered-by": "ASP.NET", "date": "Sun, 03 Aug 2025 14:10:43 GMT", "content-length": "0"}, "content_length": 0, "allowed": true, "with_auth": {"status_code": 200, "content_length": 0, "different_response": false}}, "OPTIONS": {"status_code": 200, "headers": {"server": "Microsoft-IIS/10.0", "x-powered-by": "ASP.NET", "date": "Sun, 03 Aug 2025 14:10:43 GMT", "content-length": "0"}, "content_length": 0, "allowed": true, "with_auth": {"status_code": 200, "content_length": 0, "different_response": false}}, "HEAD": {"status_code": 405, "headers": {"allow": "GET", "content-length": "137", "content-type": "application/json; charset=utf-8", "server": "Microsoft-IIS/10.0", "x-powered-by": "ASP.NET", "date": "Sun, 03 Aug 2025 14:10:43 GMT"}, "content_length": 0, "allowed": false, "with_auth": {"status_code": 405, "content_length": 0, "different_response": false}}}}, "authentication_tests": [{"method": "Basic Auth", "status_code": 200, "success": true, "content_preview": ""}, {"method": "Digest Auth", "status_code": 200, "success": true, "content_preview": ""}, {"method": "API Key - X-API-Key", "status_code": 200, "success": true, "content_preview": ""}, {"method": "API Key - Authorization", "status_code": 200, "success": true, "content_preview": ""}, {"method": "API Key - Authorization", "status_code": 200, "success": true, "content_preview": ""}, {"method": "API Key - Authorization", "status_code": 200, "success": true, "content_preview": ""}, {"method": "API Key - X-Auth-Token", "status_code": 200, "success": true, "content_preview": ""}, {"method": "API Key - X-Access-Token", "status_code": 200, "success": true, "content_preview": ""}, {"method": "API Key - API-Key", "status_code": 200, "success": true, "content_preview": ""}, {"method": "API Key - Token", "status_code": 200, "success": true, "content_preview": ""}, {"method": "POST JSON - username", "status_code": 200, "success": true, "content_preview": ""}, {"method": "POST Form - username", "status_code": 200, "success": true, "content_preview": ""}, {"method": "POST JSON - user", "status_code": 200, "success": true, "content_preview": ""}, {"method": "POST Form - user", "status_code": 200, "success": true, "content_preview": ""}, {"method": "POST JSON - email", "status_code": 200, "success": true, "content_preview": ""}, {"method": "POST Form - email", "status_code": 200, "success": true, "content_preview": ""}, {"method": "POST JSON - login", "status_code": 200, "success": true, "content_preview": ""}, {"method": "POST Form - login", "status_code": 200, "success": true, "content_preview": ""}, {"method": "POST JSON - admin", "status_code": 200, "success": true, "content_preview": ""}, {"method": "POST Form - admin", "status_code": 200, "success": true, "content_preview": ""}], "endpoint_mapping": [{"endpoint": "", "url": "http://rapi.earthlink.iq/api", "method": "GET", "status_code": 200, "content_length": 0, "accessible": true, "content_type": "", "content_preview": ""}, {"endpoint": "/login", "url": "http://rapi.earthlink.iq/api/login", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}, {"endpoint": "/auth", "url": "http://rapi.earthlink.iq/api/auth", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}, {"endpoint": "/authenticate", "url": "http://rapi.earthlink.iq/api/authenticate", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}, {"endpoint": "/token", "url": "http://rapi.earthlink.iq/api/token", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}, {"endpoint": "/users", "url": "http://rapi.earthlink.iq/api/users", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}, {"endpoint": "/subscribers", "url": "http://rapi.earthlink.iq/api/subscribers", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}, {"endpoint": "/customers", "url": "http://rapi.earthlink.iq/api/customers", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}, {"endpoint": "/clients", "url": "http://rapi.earthlink.iq/api/clients", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}, {"endpoint": "/subscriptions", "url": "http://rapi.earthlink.iq/api/subscriptions", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}, {"endpoint": "/plans", "url": "http://rapi.earthlink.iq/api/plans", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}, {"endpoint": "/packages", "url": "http://rapi.earthlink.iq/api/packages", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}, {"endpoint": "/services", "url": "http://rapi.earthlink.iq/api/services", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}, {"endpoint": "/billing", "url": "http://rapi.earthlink.iq/api/billing", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}, {"endpoint": "/payments", "url": "http://rapi.earthlink.iq/api/payments", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}, {"endpoint": "/invoices", "url": "http://rapi.earthlink.iq/api/invoices", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}, {"endpoint": "/transactions", "url": "http://rapi.earthlink.iq/api/transactions", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}, {"endpoint": "/reports", "url": "http://rapi.earthlink.iq/api/reports", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}, {"endpoint": "/stats", "url": "http://rapi.earthlink.iq/api/stats", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}, {"endpoint": "/analytics", "url": "http://rapi.earthlink.iq/api/analytics", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}, {"endpoint": "/dashboard", "url": "http://rapi.earthlink.iq/api/dashboard", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}, {"endpoint": "/admin", "url": "http://rapi.earthlink.iq/api/admin", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}, {"endpoint": "/reseller", "url": "http://rapi.earthlink.iq/api/reseller", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}, {"endpoint": "/manager", "url": "http://rapi.earthlink.iq/api/manager", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}, {"endpoint": "/v1", "url": "http://rapi.earthlink.iq/api/v1", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}, {"endpoint": "/v2", "url": "http://rapi.earthlink.iq/api/v2", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}, {"endpoint": "/v3", "url": "http://rapi.earthlink.iq/api/v3", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}, {"endpoint": "/status", "url": "http://rapi.earthlink.iq/api/status", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}, {"endpoint": "/health", "url": "http://rapi.earthlink.iq/api/health", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}, {"endpoint": "/ping", "url": "http://rapi.earthlink.iq/api/ping", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}, {"endpoint": "/version", "url": "http://rapi.earthlink.iq/api/version", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}, {"endpoint": "/docs", "url": "http://rapi.earthlink.iq/api/docs", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}, {"endpoint": "/swagger", "url": "http://rapi.earthlink.iq/api/swagger", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}, {"endpoint": "/openapi", "url": "http://rapi.earthlink.iq/api/openapi", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}, {"endpoint": "/test", "url": "http://rapi.earthlink.iq/api/test", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}, {"endpoint": "/demo", "url": "http://rapi.earthlink.iq/api/demo", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}, {"endpoint": "/sample", "url": "http://rapi.earthlink.iq/api/sample", "method": "GET", "status_code": 404, "content_length": 1245, "accessible": false, "content_type": "text/html", "content_preview": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-str"}], "data_extraction": [], "successful_requests": [{"method": "Basic Auth", "status_code": 200, "success": true, "content_preview": ""}, {"method": "Digest Auth", "status_code": 200, "success": true, "content_preview": ""}, {"method": "API Key - X-API-Key", "status_code": 200, "success": true, "content_preview": ""}, {"method": "API Key - Authorization", "status_code": 200, "success": true, "content_preview": ""}, {"method": "API Key - Authorization", "status_code": 200, "success": true, "content_preview": ""}, {"method": "API Key - Authorization", "status_code": 200, "success": true, "content_preview": ""}, {"method": "API Key - X-Auth-Token", "status_code": 200, "success": true, "content_preview": ""}, {"method": "API Key - X-Access-Token", "status_code": 200, "success": true, "content_preview": ""}, {"method": "API Key - API-Key", "status_code": 200, "success": true, "content_preview": ""}, {"method": "API Key - Token", "status_code": 200, "success": true, "content_preview": ""}, {"method": "POST JSON - username", "status_code": 200, "success": true, "content_preview": ""}, {"method": "POST Form - username", "status_code": 200, "success": true, "content_preview": ""}, {"method": "POST JSON - user", "status_code": 200, "success": true, "content_preview": ""}, {"method": "POST Form - user", "status_code": 200, "success": true, "content_preview": ""}, {"method": "POST JSON - email", "status_code": 200, "success": true, "content_preview": ""}, {"method": "POST Form - email", "status_code": 200, "success": true, "content_preview": ""}, {"method": "POST JSON - login", "status_code": 200, "success": true, "content_preview": ""}, {"method": "POST Form - login", "status_code": 200, "success": true, "content_preview": ""}, {"method": "POST JSON - admin", "status_code": 200, "success": true, "content_preview": ""}, {"method": "POST Form - admin", "status_code": 200, "success": true, "content_preview": ""}, {"endpoint": "", "url": "http://rapi.earthlink.iq/api", "method": "GET", "status_code": 200, "content_length": 0, "accessible": true, "content_type": "", "content_preview": ""}, {"type": "GET params", "params": {"action": "login"}, "status_code": 200, "content_length": 0, "success": true, "content_preview": ""}, {"type": "GET params", "params": {"method": "authenticate"}, "status_code": 200, "content_length": 0, "success": true, "content_preview": ""}, {"type": "GET params", "params": {"cmd": "auth"}, "status_code": 200, "content_length": 0, "success": true, "content_preview": ""}, {"type": "GET params", "params": {"function": "login"}, "status_code": 200, "content_length": 0, "success": true, "content_preview": ""}, {"type": "GET params", "params": {"op": "signin"}, "status_code": 200, "content_length": 0, "success": true, "content_preview": ""}, {"type": "GET params", "params": {"task": "auth"}, "status_code": 200, "content_length": 0, "success": true, "content_preview": ""}, {"type": "GET params", "params": {"mode": "login"}, "status_code": 200, "content_length": 0, "success": true, "content_preview": ""}, {"type": "GET params", "params": {"type": "auth"}, "status_code": 200, "content_length": 0, "success": true, "content_preview": ""}, {"type": "GET params", "params": {"format": "json"}, "status_code": 200, "content_length": 0, "success": true, "content_preview": ""}, {"type": "GET params", "params": {"output": "json"}, "status_code": 200, "content_length": 0, "success": true, "content_preview": ""}, {"type": "GET params", "params": {"response": "json"}, "status_code": 200, "content_length": 0, "success": true, "content_preview": ""}, {"type": "GET params", "params": {"api_key": "adham12398071@@11"}, "status_code": 200, "content_length": 0, "success": true, "content_preview": ""}, {"type": "GET params", "params": {"token": "adham12398071@@11"}, "status_code": 200, "content_length": 0, "success": true, "content_preview": ""}, {"type": "GET params", "params": {"key": "adham12398071@@11"}, "status_code": 200, "content_length": 0, "success": true, "content_preview": ""}, {"type": "GET params", "params": {"username": "admin@adhamm1", "password": "adham12398071@@11"}, "status_code": 200, "content_length": 0, "success": true, "content_preview": ""}, {"type": "GET params", "params": {"user": "admin@adhamm1", "pass": "adham12398071@@11"}, "status_code": 200, "content_length": 0, "success": true, "content_preview": ""}], "parameter_tests": [{"type": "GET params", "params": {"action": "login"}, "status_code": 200, "content_length": 0, "success": true, "content_preview": ""}, {"type": "GET params", "params": {"method": "authenticate"}, "status_code": 200, "content_length": 0, "success": true, "content_preview": ""}, {"type": "GET params", "params": {"cmd": "auth"}, "status_code": 200, "content_length": 0, "success": true, "content_preview": ""}, {"type": "GET params", "params": {"function": "login"}, "status_code": 200, "content_length": 0, "success": true, "content_preview": ""}, {"type": "GET params", "params": {"op": "signin"}, "status_code": 200, "content_length": 0, "success": true, "content_preview": ""}, {"type": "GET params", "params": {"task": "auth"}, "status_code": 200, "content_length": 0, "success": true, "content_preview": ""}, {"type": "GET params", "params": {"mode": "login"}, "status_code": 200, "content_length": 0, "success": true, "content_preview": ""}, {"type": "GET params", "params": {"type": "auth"}, "status_code": 200, "content_length": 0, "success": true, "content_preview": ""}, {"type": "GET params", "params": {"format": "json"}, "status_code": 200, "content_length": 0, "success": true, "content_preview": ""}, {"type": "GET params", "params": {"output": "json"}, "status_code": 200, "content_length": 0, "success": true, "content_preview": ""}, {"type": "GET params", "params": {"response": "json"}, "status_code": 200, "content_length": 0, "success": true, "content_preview": ""}, {"type": "GET params", "params": {"api_key": "adham12398071@@11"}, "status_code": 200, "content_length": 0, "success": true, "content_preview": ""}, {"type": "GET params", "params": {"token": "adham12398071@@11"}, "status_code": 200, "content_length": 0, "success": true, "content_preview": ""}, {"type": "GET params", "params": {"key": "adham12398071@@11"}, "status_code": 200, "content_length": 0, "success": true, "content_preview": ""}, {"type": "GET params", "params": {"username": "admin@adhamm1", "password": "adham12398071@@11"}, "status_code": 200, "content_length": 0, "success": true, "content_preview": ""}, {"type": "GET params", "params": {"user": "admin@adhamm1", "pass": "adham12398071@@11"}, "status_code": 200, "content_length": 0, "success": true, "content_preview": ""}]}