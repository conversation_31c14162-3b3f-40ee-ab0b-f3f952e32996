#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Earthlink API Client
====================
عميل API لشركة إيرثلنك للوصول إلى بيانات المشتركين والاشتراكات

المطور: مساعد الذكي
التاريخ: 2025-08-03
"""

import requests
import json
import urllib3
from urllib.parse import urljoin, urlparse
import time
import logging
from typing import Dict, List, Optional, Any
import base64
from requests.auth import HTTPBasicAuth
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# تعطيل تحذيرات SSL
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('earthlink_api.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class EarthlinkAPIClient:
    """عميل API لشركة إيرثلنك"""
    
    def __init__(self, base_url: str = "http://rapi.earthlink.iq"):
        """
        تهيئة عميل API
        
        Args:
            base_url: الرابط الأساسي للـ API
        """
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.logger = logging.getLogger(__name__)
        
        # إعداد retry strategy
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # إعداد headers افتراضية
        self.session.headers.update({
            'User-Agent': 'Earthlink-API-Client/1.0',
            'Accept': 'application/json, text/html, */*',
            'Accept-Language': 'ar,en;q=0.9',
            'Connection': 'keep-alive',
        })
        
        # تعطيل التحقق من SSL
        self.session.verify = False
        
        self.is_authenticated = False
        self.auth_token = None
        self.cookies = {}
        
    def login(self, username: str, password: str) -> bool:
        """
        تسجيل الدخول إلى النظام
        
        Args:
            username: اسم المستخدم
            password: كلمة المرور
            
        Returns:
            True إذا نجح تسجيل الدخول، False خلاف ذلك
        """
        self.logger.info(f"محاولة تسجيل الدخول للمستخدم: {username}")
        
        # طرق مختلفة لتسجيل الدخول
        login_methods = [
            self._try_basic_auth,
            self._try_form_login,
            self._try_json_login,
            self._try_api_key_login,
            self._try_session_login
        ]
        
        for method in login_methods:
            try:
                if method(username, password):
                    self.is_authenticated = True
                    self.logger.info("تم تسجيل الدخول بنجاح")
                    return True
            except Exception as e:
                self.logger.warning(f"فشل في طريقة {method.__name__}: {str(e)}")
                continue
        
        self.logger.error("فشل في تسجيل الدخول بجميع الطرق")
        return False
    
    def _try_basic_auth(self, username: str, password: str) -> bool:
        """محاولة تسجيل الدخول باستخدام Basic Authentication"""
        self.logger.info("محاولة Basic Authentication")
        
        self.session.auth = HTTPBasicAuth(username, password)
        
        try:
            response = self.session.get(f"{self.base_url}/")
            if response.status_code == 200:
                self.logger.info("نجح Basic Authentication")
                return True
        except Exception as e:
            self.logger.warning(f"فشل Basic Authentication: {str(e)}")
        
        return False
    
    def _try_form_login(self, username: str, password: str) -> bool:
        """محاولة تسجيل الدخول باستخدام نموذج HTML"""
        self.logger.info("محاولة Form Login")
        
        # محاولة الحصول على صفحة تسجيل الدخول
        login_urls = [
            f"{self.base_url}/login",
            f"{self.base_url}/auth",
            f"{self.base_url}/signin",
            f"{self.base_url}/admin",
            f"{self.base_url}/admin/login",
            f"{self.base_url}/api/login",
            f"{self.base_url}/reseller/login"
        ]
        
        for login_url in login_urls:
            try:
                # الحصول على صفحة تسجيل الدخول
                response = self.session.get(login_url)
                if response.status_code == 200:
                    # محاولة إرسال بيانات تسجيل الدخول
                    login_data = {
                        'username': username,
                        'password': password,
                        'user': username,
                        'pass': password,
                        'email': username,
                        'login': username,
                        'admin': username,
                        'reseller': username
                    }
                    
                    response = self.session.post(login_url, data=login_data)
                    
                    # فحص نجاح تسجيل الدخول
                    if (response.status_code == 200 and 
                        ('dashboard' in response.text.lower() or 
                         'welcome' in response.text.lower() or
                         'logout' in response.text.lower() or
                         'admin' in response.text.lower())):
                        self.logger.info(f"نجح Form Login في {login_url}")
                        return True
                        
            except Exception as e:
                self.logger.warning(f"فشل Form Login في {login_url}: {str(e)}")
                continue
        
        return False
    
    def _try_json_login(self, username: str, password: str) -> bool:
        """محاولة تسجيل الدخول باستخدام JSON API"""
        self.logger.info("محاولة JSON Login")
        
        api_endpoints = [
            f"{self.base_url}/api/auth",
            f"{self.base_url}/api/login",
            f"{self.base_url}/auth/login",
            f"{self.base_url}/login/api",
            f"{self.base_url}/reseller/api/auth"
        ]
        
        for endpoint in api_endpoints:
            try:
                login_data = {
                    'username': username,
                    'password': password
                }
                
                headers = {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
                
                response = self.session.post(
                    endpoint, 
                    json=login_data, 
                    headers=headers
                )
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        if 'token' in data or 'access_token' in data or 'success' in data:
                            self.auth_token = data.get('token') or data.get('access_token')
                            if self.auth_token:
                                self.session.headers['Authorization'] = f'Bearer {self.auth_token}'
                            self.logger.info(f"نجح JSON Login في {endpoint}")
                            return True
                    except json.JSONDecodeError:
                        pass
                        
            except Exception as e:
                self.logger.warning(f"فشل JSON Login في {endpoint}: {str(e)}")
                continue
        
        return False
    
    def _try_api_key_login(self, username: str, password: str) -> bool:
        """محاولة تسجيل الدخول باستخدام API Key"""
        self.logger.info("محاولة API Key Login")
        
        # محاولة استخدام كلمة المرور كـ API key
        api_key_headers = [
            {'X-API-Key': password},
            {'Authorization': f'ApiKey {password}'},
            {'Authorization': f'Token {password}'},
            {'X-Auth-Token': password},
            {'X-Access-Token': password}
        ]
        
        for headers in api_key_headers:
            try:
                self.session.headers.update(headers)
                response = self.session.get(f"{self.base_url}/")
                
                if response.status_code == 200:
                    self.logger.info("نجح API Key Login")
                    return True
                    
            except Exception as e:
                self.logger.warning(f"فشل API Key Login: {str(e)}")
                continue
        
        return False
    
    def _try_session_login(self, username: str, password: str) -> bool:
        """محاولة تسجيل الدخول باستخدام Session"""
        self.logger.info("محاولة Session Login")
        
        try:
            # محاولة الحصول على session
            response = self.session.get(f"{self.base_url}/")
            
            # محاولة تسجيل الدخول مع session cookies
            login_data = {
                'username': username,
                'password': password
            }
            
            response = self.session.post(f"{self.base_url}/", data=login_data)
            
            if response.status_code == 200:
                self.logger.info("نجح Session Login")
                return True
                
        except Exception as e:
            self.logger.warning(f"فشل Session Login: {str(e)}")
        
        return False
    
    def get_subscribers(self) -> List[Dict]:
        """
        الحصول على قائمة المشتركين
        
        Returns:
            قائمة بالمشتركين
        """
        if not self.is_authenticated:
            self.logger.error("يجب تسجيل الدخول أولاً")
            return []
        
        self.logger.info("جلب قائمة المشتركين")
        
        # نقاط نهاية محتملة للمشتركين
        endpoints = [
            '/api/subscribers',
            '/api/users',
            '/api/customers',
            '/subscribers',
            '/users',
            '/customers',
            '/admin/subscribers',
            '/admin/users',
            '/reseller/subscribers'
        ]
        
        for endpoint in endpoints:
            try:
                response = self.session.get(f"{self.base_url}{endpoint}")
                if response.status_code == 200:
                    try:
                        data = response.json()
                        if isinstance(data, list) or (isinstance(data, dict) and 'data' in data):
                            self.logger.info(f"تم جلب المشتركين من {endpoint}")
                            return data if isinstance(data, list) else data['data']
                    except json.JSONDecodeError:
                        # محاولة تحليل HTML
                        if 'subscriber' in response.text.lower():
                            self.logger.info(f"وجدت بيانات HTML في {endpoint}")
                            return [{'html_content': response.text}]
                            
            except Exception as e:
                self.logger.warning(f"فشل في جلب المشتركين من {endpoint}: {str(e)}")
                continue
        
        return []
    
    def get_subscriptions(self) -> List[Dict]:
        """
        الحصول على قائمة الاشتراكات
        
        Returns:
            قائمة بالاشتراكات
        """
        if not self.is_authenticated:
            self.logger.error("يجب تسجيل الدخول أولاً")
            return []
        
        self.logger.info("جلب قائمة الاشتراكات")
        
        endpoints = [
            '/api/subscriptions',
            '/api/plans',
            '/api/packages',
            '/subscriptions',
            '/plans',
            '/packages',
            '/admin/subscriptions',
            '/reseller/subscriptions'
        ]
        
        for endpoint in endpoints:
            try:
                response = self.session.get(f"{self.base_url}{endpoint}")
                if response.status_code == 200:
                    try:
                        data = response.json()
                        if isinstance(data, list) or (isinstance(data, dict) and 'data' in data):
                            self.logger.info(f"تم جلب الاشتراكات من {endpoint}")
                            return data if isinstance(data, list) else data['data']
                    except json.JSONDecodeError:
                        if 'subscription' in response.text.lower() or 'plan' in response.text.lower():
                            self.logger.info(f"وجدت بيانات HTML في {endpoint}")
                            return [{'html_content': response.text}]
                            
            except Exception as e:
                self.logger.warning(f"فشل في جلب الاشتراكات من {endpoint}: {str(e)}")
                continue
        
        return []
    
    def explore_api(self) -> Dict[str, Any]:
        """
        استكشاف API واكتشاف النقاط المتاحة
        
        Returns:
            معلومات عن API المكتشف
        """
        if not self.is_authenticated:
            self.logger.error("يجب تسجيل الدخول أولاً")
            return {}
        
        self.logger.info("استكشاف API")
        
        discovered = {
            'endpoints': [],
            'data_found': {},
            'errors': []
        }
        
        # قائمة بالنقاط المحتملة
        potential_endpoints = [
            '/', '/api', '/admin', '/reseller',
            '/api/users', '/api/subscribers', '/api/customers',
            '/api/subscriptions', '/api/plans', '/api/packages',
            '/api/billing', '/api/payments', '/api/invoices',
            '/api/reports', '/api/stats', '/api/dashboard',
            '/users', '/subscribers', '/customers',
            '/subscriptions', '/plans', '/packages',
            '/billing', '/payments', '/invoices',
            '/reports', '/stats', '/dashboard',
            '/admin/users', '/admin/subscribers', '/admin/dashboard',
            '/reseller/users', '/reseller/subscribers', '/reseller/dashboard'
        ]
        
        for endpoint in potential_endpoints:
            try:
                response = self.session.get(f"{self.base_url}{endpoint}")
                
                if response.status_code == 200:
                    discovered['endpoints'].append({
                        'url': endpoint,
                        'status': response.status_code,
                        'content_type': response.headers.get('content-type', ''),
                        'size': len(response.content)
                    })
                    
                    # محاولة تحليل المحتوى
                    try:
                        if 'application/json' in response.headers.get('content-type', ''):
                            data = response.json()
                            discovered['data_found'][endpoint] = data
                        else:
                            # فحص HTML للكلمات المفتاحية
                            content = response.text.lower()
                            keywords = ['subscriber', 'user', 'customer', 'subscription', 'plan', 'package', 'billing']
                            found_keywords = [kw for kw in keywords if kw in content]
                            if found_keywords:
                                discovered['data_found'][endpoint] = {
                                    'type': 'html',
                                    'keywords_found': found_keywords,
                                    'content_preview': response.text[:500]
                                }
                    except:
                        pass
                        
            except Exception as e:
                discovered['errors'].append({
                    'endpoint': endpoint,
                    'error': str(e)
                })
        
        return discovered


def main():
    """الدالة الرئيسية"""
    print("🌐 عميل API إيرثلنك")
    print("=" * 50)
    
    # بيانات تسجيل الدخول
    username = "admin@adhamm1"
    password = "adham12398071@@11"
    
    # إنشاء عميل API
    client = EarthlinkAPIClient()
    
    # محاولة تسجيل الدخول
    print(f"🔐 محاولة تسجيل الدخول للمستخدم: {username}")
    
    if client.login(username, password):
        print("✅ تم تسجيل الدخول بنجاح!")
        
        # استكشاف API
        print("\n🔍 استكشاف API...")
        api_info = client.explore_api()
        
        print(f"\n📊 تم اكتشاف {len(api_info['endpoints'])} نقطة نهاية")
        for endpoint in api_info['endpoints']:
            print(f"  - {endpoint['url']} ({endpoint['status']}) - {endpoint['size']} bytes")
        
        # جلب المشتركين
        print("\n👥 جلب قائمة المشتركين...")
        subscribers = client.get_subscribers()
        print(f"تم العثور على {len(subscribers)} مشترك")
        
        # جلب الاشتراكات
        print("\n📋 جلب قائمة الاشتراكات...")
        subscriptions = client.get_subscriptions()
        print(f"تم العثور على {len(subscriptions)} اشتراك")
        
        # حفظ النتائج
        results = {
            'api_info': api_info,
            'subscribers': subscribers,
            'subscriptions': subscriptions,
            'timestamp': time.time()
        }
        
        with open('earthlink_data.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print("\n💾 تم حفظ البيانات في earthlink_data.json")
        
    else:
        print("❌ فشل في تسجيل الدخول")


if __name__ == "__main__":
    main()
