# 🌐 عميل API إيرثلنك

أداة شاملة للوصول إلى بيانات إيرثلنك API وإدارة المشتركين والاشتراكات.

## 📋 المحتويات

- [المتطلبات](#المتطلبات)
- [التثبيت](#التثبيت)
- [الاستخدام](#الاستخدام)
- [الملفات](#الملفات)
- [المميزات](#المميزات)

## 🔧 المتطلبات

- Python 3.7+
- Google Chrome (للـ Selenium)
- ChromeDriver (سيتم تحميله تلقائياً)

## 📦 التثبيت

1. **استنساخ المشروع:**
```bash
git clone <repository-url>
cd earthlink-api-client
```

2. **تثبيت المتطلبات:**
```bash
pip install -r requirements.txt
```

أو استخدم الأداة المدمجة:
```bash
python run.py
# اختر الخيار 1 لتثبيت المتطلبات
```

## 🚀 الاستخدام

### 1. التشغيل السريع
```bash
python run.py
```

### 2. استخدام عميل API مباشرة
```python
from earthlink_api_client import EarthlinkAPIClient

# إنشاء عميل
client = EarthlinkAPIClient("http://rapi.earthlink.iq")

# تسجيل الدخول
if client.login("admin@adhamm1", "adham12398071@@11"):
    # جلب المشتركين
    subscribers = client.get_subscribers()
    
    # جلب الاشتراكات
    subscriptions = client.get_subscriptions()
    
    # استكشاف API
    api_info = client.explore_api()
```

### 3. استخدام واجهة الويب
```bash
python web_interface.py
```
ثم افتح المتصفح على: `http://localhost:5000`

### 4. استخدام مستكشف Selenium
```python
from selenium_explorer import EarthlinkSeleniumExplorer

explorer = EarthlinkSeleniumExplorer(headless=False)
results = explorer.explore_site("admin@adhamm1", "adham12398071@@11")
```

## 📁 الملفات

| الملف | الوصف |
|-------|--------|
| `earthlink_api_client.py` | عميل API الرئيسي |
| `web_interface.py` | واجهة ويب Flask |
| `selenium_explorer.py` | مستكشف باستخدام Selenium |
| `run.py` | أداة التشغيل السريع |
| `templates/index.html` | قالب واجهة الويب |
| `requirements.txt` | المتطلبات |

## ✨ المميزات

### 🔐 طرق تسجيل الدخول المتعددة
- Basic Authentication
- Form-based Login
- JSON API Login
- API Key Authentication
- Session-based Login

### 📊 استكشاف شامل للـ API
- اكتشاف نقاط النهاية تلقائياً
- استخراج البيانات من HTML و JSON
- فحص الصفحات المحمية
- تسجيل مفصل للعمليات

### 🌐 واجهة ويب سهلة الاستخدام
- تسجيل دخول تفاعلي
- عرض البيانات بتنسيق JSON
- واجهة عربية كاملة
- تصميم متجاوب

### 🔍 استكشاف متقدم بـ Selenium
- محاكاة متصفح حقيقي
- اكتشاف النماذج تلقائياً
- أخذ لقطات شاشة
- استخراج البيانات من الجداول

## 📝 بيانات تسجيل الدخول

```
الرابط: http://rapi.earthlink.iq
اسم المستخدم: admin@adhamm1
كلمة المرور: adham12398071@@11
```

## 🔧 الإعدادات

### تخصيص رابط API
```python
client = EarthlinkAPIClient("http://your-api-url.com")
```

### تشغيل Selenium في الخلفية
```python
explorer = EarthlinkSeleniumExplorer(headless=True)
```

### تغيير منفذ واجهة الويب
```python
app.run(port=8080)
```

## 📊 ملفات النتائج

- `earthlink_data.json` - بيانات API الأساسية
- `api_test_results.json` - نتائج اختبار API
- `selenium_results.json` - نتائج استكشاف Selenium
- `earthlink_api.log` - سجل عميل API
- `selenium_explorer.log` - سجل مستكشف Selenium

## 🛠️ استكشاف الأخطاء

### خطأ في الاتصال
```
❌ فشل في تسجيل الدخول
```
**الحل:** تحقق من:
- صحة بيانات تسجيل الدخول
- الاتصال بالإنترنت
- توفر الخادم

### خطأ في Selenium
```
❌ فشل في إعداد المتصفح
```
**الحل:**
- تثبيت Google Chrome
- تحديث ChromeDriver
- تشغيل كمدير

### خطأ في واجهة الويب
```
❌ خطأ في تشغيل واجهة الويب
```
**الحل:**
- تحقق من توفر المنفذ 5000
- تثبيت Flask
- تشغيل كمدير

## 📞 الدعم

للحصول على المساعدة:
1. تحقق من ملفات السجل
2. راجع رسائل الخطأ
3. تأكد من صحة بيانات تسجيل الدخول

## 🔄 التحديثات

لتحديث الأداة:
```bash
git pull origin main
pip install -r requirements.txt --upgrade
```

## 📄 الترخيص

هذا المشروع مخصص للاستخدام التعليمي والتطويري.

---

**ملاحظة:** تأكد من أن لديك الصلاحيات اللازمة للوصول إلى API إيرثلنك قبل الاستخدام.
