<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عميل API إيرثلنك</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .main-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 1200px;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px 15px 0 0;
            text-align: center;
        }
        .login-section {
            padding: 30px;
            border-bottom: 1px solid #eee;
        }
        .data-section {
            padding: 30px;
            display: none;
        }
        .btn-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            transition: all 0.3s;
        }
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .status-online { background-color: #28a745; }
        .status-offline { background-color: #dc3545; }
        .data-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
        }
        .json-viewer {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        .spinner-border {
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- Header -->
            <div class="header">
                <h1><i class="fas fa-globe"></i> عميل API إيرثلنك</h1>
                <p class="mb-0">أداة للوصول إلى بيانات المشتركين والاشتراكات</p>
                <div class="mt-2">
                    <span id="connection-status">
                        <span class="status-indicator status-offline"></span>
                        غير متصل
                    </span>
                </div>
            </div>

            <!-- Login Section -->
            <div class="login-section" id="login-section">
                <h3><i class="fas fa-sign-in-alt"></i> تسجيل الدخول</h3>
                <form id="login-form">
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">رابط API:</label>
                            <input type="url" class="form-control" id="base-url" value="http://rapi.earthlink.iq" required>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">اسم المستخدم:</label>
                            <input type="text" class="form-control" id="username" value="admin@adhamm1" required>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">كلمة المرور:</label>
                            <input type="password" class="form-control" id="password" value="adham12398071@@11" required>
                        </div>
                    </div>
                    <div class="mt-3">
                        <button type="submit" class="btn btn-custom">
                            <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                        </button>
                    </div>
                </form>
                
                <div class="loading" id="login-loading">
                    <div class="spinner-border" role="status"></div>
                    <p class="mt-2">جاري تسجيل الدخول...</p>
                </div>
            </div>

            <!-- Data Section -->
            <div class="data-section" id="data-section">
                <div class="row">
                    <div class="col-md-12">
                        <h3><i class="fas fa-database"></i> لوحة التحكم</h3>
                        <div class="btn-group mb-3" role="group">
                            <button type="button" class="btn btn-custom" onclick="exploreAPI()">
                                <i class="fas fa-search"></i> استكشاف API
                            </button>
                            <button type="button" class="btn btn-custom" onclick="getSubscribers()">
                                <i class="fas fa-users"></i> المشتركين
                            </button>
                            <button type="button" class="btn btn-custom" onclick="getSubscriptions()">
                                <i class="fas fa-list"></i> الاشتراكات
                            </button>
                            <button type="button" class="btn btn-outline-danger" onclick="logout()">
                                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                            </button>
                        </div>
                    </div>
                </div>

                <div class="loading" id="data-loading">
                    <div class="spinner-border" role="status"></div>
                    <p class="mt-2">جاري تحميل البيانات...</p>
                </div>

                <div id="results-container"></div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // متغيرات عامة
        let isLoggedIn = false;

        // فحص حالة الاتصال عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            checkStatus();
        });

        // تسجيل الدخول
        document.getElementById('login-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const baseUrl = document.getElementById('base-url').value;
            
            showLoading('login-loading');
            
            fetch('/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: username,
                    password: password,
                    base_url: baseUrl
                })
            })
            .then(response => response.json())
            .then(data => {
                hideLoading('login-loading');
                
                if (data.success) {
                    showSuccess('تم تسجيل الدخول بنجاح!');
                    isLoggedIn = true;
                    updateConnectionStatus(true, username);
                    showDataSection();
                } else {
                    showError(data.message);
                }
            })
            .catch(error => {
                hideLoading('login-loading');
                showError('خطأ في الاتصال: ' + error.message);
            });
        });

        // استكشاف API
        function exploreAPI() {
            showLoading('data-loading');
            
            fetch('/explore')
            .then(response => response.json())
            .then(data => {
                hideLoading('data-loading');
                
                if (data.success) {
                    displayResults('استكشاف API', data.data);
                } else {
                    showError(data.message);
                }
            })
            .catch(error => {
                hideLoading('data-loading');
                showError('خطأ: ' + error.message);
            });
        }

        // جلب المشتركين
        function getSubscribers() {
            showLoading('data-loading');
            
            fetch('/subscribers')
            .then(response => response.json())
            .then(data => {
                hideLoading('data-loading');
                
                if (data.success) {
                    displayResults(`المشتركين (${data.count})`, data.data);
                } else {
                    showError(data.message);
                }
            })
            .catch(error => {
                hideLoading('data-loading');
                showError('خطأ: ' + error.message);
            });
        }

        // جلب الاشتراكات
        function getSubscriptions() {
            showLoading('data-loading');
            
            fetch('/subscriptions')
            .then(response => response.json())
            .then(data => {
                hideLoading('data-loading');
                
                if (data.success) {
                    displayResults(`الاشتراكات (${data.count})`, data.data);
                } else {
                    showError(data.message);
                }
            })
            .catch(error => {
                hideLoading('data-loading');
                showError('خطأ: ' + error.message);
            });
        }

        // تسجيل الخروج
        function logout() {
            fetch('/logout')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    isLoggedIn = false;
                    updateConnectionStatus(false);
                    showLoginSection();
                    showSuccess('تم تسجيل الخروج');
                }
            });
        }

        // فحص حالة الاتصال
        function checkStatus() {
            fetch('/status')
            .then(response => response.json())
            .then(data => {
                if (data.logged_in) {
                    isLoggedIn = true;
                    updateConnectionStatus(true, data.username);
                    showDataSection();
                } else {
                    isLoggedIn = false;
                    updateConnectionStatus(false);
                    showLoginSection();
                }
            });
        }

        // عرض النتائج
        function displayResults(title, data) {
            const container = document.getElementById('results-container');
            
            const html = `
                <div class="data-card">
                    <h4><i class="fas fa-chart-bar"></i> ${title}</h4>
                    <div class="json-viewer">
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                </div>
            `;
            
            container.innerHTML = html;
        }

        // تحديث حالة الاتصال
        function updateConnectionStatus(connected, username = '') {
            const statusElement = document.getElementById('connection-status');
            
            if (connected) {
                statusElement.innerHTML = `
                    <span class="status-indicator status-online"></span>
                    متصل - ${username}
                `;
            } else {
                statusElement.innerHTML = `
                    <span class="status-indicator status-offline"></span>
                    غير متصل
                `;
            }
        }

        // إظهار قسم تسجيل الدخول
        function showLoginSection() {
            document.getElementById('login-section').style.display = 'block';
            document.getElementById('data-section').style.display = 'none';
        }

        // إظهار قسم البيانات
        function showDataSection() {
            document.getElementById('login-section').style.display = 'none';
            document.getElementById('data-section').style.display = 'block';
        }

        // إظهار التحميل
        function showLoading(elementId) {
            document.getElementById(elementId).style.display = 'block';
        }

        // إخفاء التحميل
        function hideLoading(elementId) {
            document.getElementById(elementId).style.display = 'none';
        }

        // إظهار رسالة نجاح
        function showSuccess(message) {
            alert('✅ ' + message);
        }

        // إظهار رسالة خطأ
        function showError(message) {
            alert('❌ ' + message);
        }
    </script>
</body>
</html>
